'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/components/ui/use-toast';
import { 
  Settings, 
  Bell, 
  Shield, 
  CreditCard, 
  Smartphone,
  Mail,
  Save,
  Loader2,
  ArrowLeft,
  AlertCircle,
  CheckCircle,
  Eye,
  EyeOff
} from 'lucide-react';

interface VendorSettings {
  // Notification Settings
  emailNotifications: boolean;
  smsNotifications: boolean;
  pushNotifications: boolean;
  orderNotifications: boolean;
  paymentNotifications: boolean;
  marketingEmails: boolean;
  
  // Privacy Settings
  profileVisibility: 'public' | 'private' | 'verified_only';
  showContactInfo: boolean;
  showBusinessHours: boolean;
  allowDirectMessages: boolean;
  
  // Payment Settings
  autoWithdraw: boolean;
  withdrawalThreshold: number;
  preferredPaymentMethod: string;
  
  // Business Settings
  acceptOnlineOrders: boolean;
  requireOrderConfirmation: boolean;
  autoAcceptOrders: boolean;
  businessHoursEnabled: boolean;
  
  // Security Settings
  twoFactorEnabled: boolean;
  loginNotifications: boolean;
  sessionTimeout: number;
}

export default function VendorSettingsPage() {
  const router = useRouter();
  const { toast } = useToast();
  const [settings, setSettings] = useState<VendorSettings>({
    // Default values
    emailNotifications: true,
    smsNotifications: false,
    pushNotifications: true,
    orderNotifications: true,
    paymentNotifications: true,
    marketingEmails: false,
    profileVisibility: 'public',
    showContactInfo: true,
    showBusinessHours: true,
    allowDirectMessages: true,
    autoWithdraw: false,
    withdrawalThreshold: 1000,
    preferredPaymentMethod: 'bank_transfer',
    acceptOnlineOrders: true,
    requireOrderConfirmation: true,
    autoAcceptOrders: false,
    businessHoursEnabled: true,
    twoFactorEnabled: false,
    loginNotifications: true,
    sessionTimeout: 30,
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    try {
      setLoading(true);
      // For now, we'll use default settings since the API endpoint doesn't exist yet
      // In a real implementation, you would fetch from /api/vendors/settings
      setSettings(settings);
    } catch (err) {
      console.error('Error fetching settings:', err);
      setError('Failed to load settings');
    } finally {
      setLoading(false);
    }
  };

  const handleSettingChange = (key: keyof VendorSettings, value: any) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      
      // For now, we'll just simulate saving
      // In a real implementation, you would POST to /api/vendors/settings
      await new Promise(resolve => setTimeout(resolve, 1000));

      toast({
        title: 'Success',
        description: 'Your settings have been saved successfully.',
      });
    } catch (err) {
      console.error('Error saving settings:', err);
      toast({
        title: 'Error',
        description: 'Failed to save settings. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2">Loading settings...</span>
      </div>
    );
  }

  if (error) {
    return (
      <Card className="max-w-md mx-auto">
        <CardHeader>
          <CardTitle className="text-center text-red-500 flex items-center justify-center gap-2">
            <AlertCircle className="h-5 w-5" />
            Error
          </CardTitle>
        </CardHeader>
        <CardContent className="text-center">
          <p className="text-muted-foreground mb-4">{error}</p>
          <Button onClick={() => router.push('/dashboard/vendor')}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Dashboard
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4">
      {/* Header */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <Settings className="h-8 w-8" />
            Vendor Settings
          </h1>
          <p className="text-muted-foreground mt-1">
            Manage your account preferences and business settings
          </p>
        </div>
        
        <div className="flex items-center gap-4">
          <Button onClick={handleSave} disabled={saving}>
            {saving ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Save Settings
              </>
            )}
          </Button>
          <Button 
            variant="outline" 
            onClick={() => router.push('/dashboard/vendor')}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Dashboard
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Notification Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Bell className="h-5 w-5" />
              Notification Settings
            </CardTitle>
            <CardDescription>
              Choose how you want to be notified about important events
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="emailNotifications">Email Notifications</Label>
                <p className="text-sm text-muted-foreground">Receive notifications via email</p>
              </div>
              <Switch
                id="emailNotifications"
                checked={settings.emailNotifications}
                onCheckedChange={(checked) => handleSettingChange('emailNotifications', checked)}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="smsNotifications">SMS Notifications</Label>
                <p className="text-sm text-muted-foreground">Receive notifications via SMS</p>
              </div>
              <Switch
                id="smsNotifications"
                checked={settings.smsNotifications}
                onCheckedChange={(checked) => handleSettingChange('smsNotifications', checked)}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="orderNotifications">Order Notifications</Label>
                <p className="text-sm text-muted-foreground">Get notified about new orders</p>
              </div>
              <Switch
                id="orderNotifications"
                checked={settings.orderNotifications}
                onCheckedChange={(checked) => handleSettingChange('orderNotifications', checked)}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="paymentNotifications">Payment Notifications</Label>
                <p className="text-sm text-muted-foreground">Get notified about payments</p>
              </div>
              <Switch
                id="paymentNotifications"
                checked={settings.paymentNotifications}
                onCheckedChange={(checked) => handleSettingChange('paymentNotifications', checked)}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="marketingEmails">Marketing Emails</Label>
                <p className="text-sm text-muted-foreground">Receive promotional emails</p>
              </div>
              <Switch
                id="marketingEmails"
                checked={settings.marketingEmails}
                onCheckedChange={(checked) => handleSettingChange('marketingEmails', checked)}
              />
            </div>
          </CardContent>
        </Card>

        {/* Privacy Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Eye className="h-5 w-5" />
              Privacy Settings
            </CardTitle>
            <CardDescription>
              Control who can see your business information
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="profileVisibility">Profile Visibility</Label>
              <Select
                value={settings.profileVisibility}
                onValueChange={(value) => handleSettingChange('profileVisibility', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="public">Public - Everyone can see</SelectItem>
                  <SelectItem value="verified_only">Verified Users Only</SelectItem>
                  <SelectItem value="private">Private - Hidden from search</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="showContactInfo">Show Contact Information</Label>
                <p className="text-sm text-muted-foreground">Display phone and email publicly</p>
              </div>
              <Switch
                id="showContactInfo"
                checked={settings.showContactInfo}
                onCheckedChange={(checked) => handleSettingChange('showContactInfo', checked)}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="showBusinessHours">Show Business Hours</Label>
                <p className="text-sm text-muted-foreground">Display operating hours</p>
              </div>
              <Switch
                id="showBusinessHours"
                checked={settings.showBusinessHours}
                onCheckedChange={(checked) => handleSettingChange('showBusinessHours', checked)}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="allowDirectMessages">Allow Direct Messages</Label>
                <p className="text-sm text-muted-foreground">Let customers message you directly</p>
              </div>
              <Switch
                id="allowDirectMessages"
                checked={settings.allowDirectMessages}
                onCheckedChange={(checked) => handleSettingChange('allowDirectMessages', checked)}
              />
            </div>
          </CardContent>
        </Card>

        {/* Payment Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CreditCard className="h-5 w-5" />
              Payment Settings
            </CardTitle>
            <CardDescription>
              Configure your payment and withdrawal preferences
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="autoWithdraw">Auto Withdraw</Label>
                <p className="text-sm text-muted-foreground">Automatically withdraw earnings</p>
              </div>
              <Switch
                id="autoWithdraw"
                checked={settings.autoWithdraw}
                onCheckedChange={(checked) => handleSettingChange('autoWithdraw', checked)}
              />
            </div>
            
            {settings.autoWithdraw && (
              <div className="space-y-2">
                <Label htmlFor="withdrawalThreshold">Withdrawal Threshold (ZMW)</Label>
                <Input
                  id="withdrawalThreshold"
                  type="number"
                  value={settings.withdrawalThreshold}
                  onChange={(e) => handleSettingChange('withdrawalThreshold', parseFloat(e.target.value))}
                  placeholder="1000"
                />
                <p className="text-sm text-muted-foreground">
                  Minimum amount before auto withdrawal
                </p>
              </div>
            )}
            
            <div className="space-y-2">
              <Label htmlFor="preferredPaymentMethod">Preferred Payment Method</Label>
              <Select
                value={settings.preferredPaymentMethod}
                onValueChange={(value) => handleSettingChange('preferredPaymentMethod', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="bank_transfer">Bank Transfer</SelectItem>
                  <SelectItem value="mobile_money">Mobile Money</SelectItem>
                  <SelectItem value="paypal">PayPal</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Business Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Business Settings
            </CardTitle>
            <CardDescription>
              Configure how your business operates
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="acceptOnlineOrders">Accept Online Orders</Label>
                <p className="text-sm text-muted-foreground">Allow customers to place orders online</p>
              </div>
              <Switch
                id="acceptOnlineOrders"
                checked={settings.acceptOnlineOrders}
                onCheckedChange={(checked) => handleSettingChange('acceptOnlineOrders', checked)}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="requireOrderConfirmation">Require Order Confirmation</Label>
                <p className="text-sm text-muted-foreground">Manually confirm each order</p>
              </div>
              <Switch
                id="requireOrderConfirmation"
                checked={settings.requireOrderConfirmation}
                onCheckedChange={(checked) => handleSettingChange('requireOrderConfirmation', checked)}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="businessHoursEnabled">Business Hours Enabled</Label>
                <p className="text-sm text-muted-foreground">Only accept orders during business hours</p>
              </div>
              <Switch
                id="businessHoursEnabled"
                checked={settings.businessHoursEnabled}
                onCheckedChange={(checked) => handleSettingChange('businessHoursEnabled', checked)}
              />
            </div>
          </CardContent>
        </Card>

        {/* Security Settings */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Security Settings
            </CardTitle>
            <CardDescription>
              Protect your account with additional security measures
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="twoFactorEnabled">Two-Factor Authentication</Label>
                    <p className="text-sm text-muted-foreground">Add an extra layer of security</p>
                  </div>
                  <Switch
                    id="twoFactorEnabled"
                    checked={settings.twoFactorEnabled}
                    onCheckedChange={(checked) => handleSettingChange('twoFactorEnabled', checked)}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="loginNotifications">Login Notifications</Label>
                    <p className="text-sm text-muted-foreground">Get notified of new logins</p>
                  </div>
                  <Switch
                    id="loginNotifications"
                    checked={settings.loginNotifications}
                    onCheckedChange={(checked) => handleSettingChange('loginNotifications', checked)}
                  />
                </div>
              </div>
              
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="sessionTimeout">Session Timeout (minutes)</Label>
                  <Select
                    value={settings.sessionTimeout.toString()}
                    onValueChange={(value) => handleSettingChange('sessionTimeout', parseInt(value))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="15">15 minutes</SelectItem>
                      <SelectItem value="30">30 minutes</SelectItem>
                      <SelectItem value="60">1 hour</SelectItem>
                      <SelectItem value="120">2 hours</SelectItem>
                      <SelectItem value="0">Never</SelectItem>
                    </SelectContent>
                  </Select>
                  <p className="text-sm text-muted-foreground">
                    Automatically log out after inactivity
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Save Button (Mobile) */}
      <div className="mt-8 lg:hidden">
        <Button onClick={handleSave} disabled={saving} className="w-full">
          {saving ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Saving Settings...
            </>
          ) : (
            <>
              <Save className="mr-2 h-4 w-4" />
              Save All Settings
            </>
          )}
        </Button>
      </div>
    </div>
  );
}
