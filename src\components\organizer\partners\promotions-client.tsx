'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Switch } from '@/components/ui/switch';
import { toast } from 'sonner';
import {
  Search,
  Eye,
  Plus,
  Tag,
  Calendar,
  TrendingUp,
  Users,
  ChevronLeft,
  PlusCircle,
  Percent
} from 'lucide-react';
import Link from 'next/link';
import { format } from 'date-fns';

interface EventPromotion {
  id: string;
  title: string;
  description: string;
  discountType: 'PERCENTAGE' | 'FIXED_AMOUNT';
  discountValue: number;
  startDate: string;
  endDate: string;
  isActive: boolean;
  usageCount: number;
  maxUsage?: number;
  partner: {
    id: string;
    businessName: string;
    partnerType: string;
  };
  event: {
    id: string;
    title: string;
    startDate: string;
  };
  createdAt: string;
}

export default function OrganizerPartnerPromotionsClient() {
  const [promotions, setPromotions] = useState<EventPromotion[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedPromotion, setSelectedPromotion] = useState<EventPromotion | null>(null);

  // Fetch promotions
  const fetchPromotions = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        ...(searchTerm && { search: searchTerm })
      });

      const response = await fetch(`/api/organizer/partners/promotions?${params}`);

      if (!response.ok) {
        throw new Error('Failed to fetch promotions');
      }

      const data = await response.json();
      setPromotions(data.promotions || []);
    } catch (error) {
      console.error('Error fetching promotions:', error);
      toast.error('Failed to load promotions');
    } finally {
      setLoading(false);
    }
  };

  // Get status badge variant
  const getStatusBadge = (promotion: EventPromotion) => {
    const now = new Date();
    const endDate = new Date(promotion.endDate);

    if (!promotion.isActive) return { variant: 'secondary', text: 'Inactive' };
    if (endDate < now) return { variant: 'destructive', text: 'Expired' };
    return { variant: 'default', text: 'Active' };
  };

  // Format discount value
  const formatDiscount = (promotion: EventPromotion) => {
    if (promotion.discountType === 'PERCENTAGE') {
      return `${promotion.discountValue}%`;
    }
    return `$${promotion.discountValue}`;
  };

  // Toggle promotion status
  const togglePromotionStatus = async (promotionId: string, currentStatus: boolean) => {
    try {
      const response = await fetch(`/api/organizer/partners/promotions/${promotionId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          isActive: !currentStatus,
        }),
      });

      if (response.ok) {
        toast.success(`Promotion ${!currentStatus ? 'activated' : 'deactivated'} successfully`);
        fetchPromotions(); // Refresh the list
      } else {
        const errorData = await response.json();
        toast.error(errorData.error || 'Failed to update promotion status');
      }
    } catch (error) {
      console.error('Error toggling promotion status:', error);
      toast.error('Failed to update promotion status');
    }
  };

  useEffect(() => {
    fetchPromotions();
  }, []);

  const activePromotions = promotions.filter(p => p.isActive).length;
  const totalUsage = promotions.reduce((sum, p) => sum + p.usageCount, 0);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
        <div>
          <div className="flex items-center gap-2 mb-2">
            <Button variant="ghost" size="sm" asChild>
              <Link href="/dashboard/organizer/partners">
                <ChevronLeft className="h-4 w-4 mr-1" />
                Back to Partners
              </Link>
            </Button>
          </div>
          <h1 className="text-3xl font-bold">Partner Promotions</h1>
          <p className="text-gray-500 mt-1">
            Manage promotional campaigns with your event partners
          </p>
        </div>
        <div className="mt-4 md:mt-0">
          <Button>
            <PlusCircle className="mr-2 h-4 w-4" />
            Create Promotion
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500 flex items-center">
              <Tag className="h-4 w-4 mr-2" />
              Total Promotions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{promotions.length}</div>
            <p className="text-xs text-gray-500 mt-1">All promotions</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500 flex items-center">
              <TrendingUp className="h-4 w-4 mr-2" />
              Active Promotions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{activePromotions}</div>
            <p className="text-xs text-gray-500 mt-1">Currently running</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500 flex items-center">
              <Users className="h-4 w-4 mr-2" />
              Total Usage
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalUsage}</div>
            <p className="text-xs text-gray-500 mt-1">Times used</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500 flex items-center">
              <Calendar className="h-4 w-4 mr-2" />
              Upcoming Events
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">2</div>
            <p className="text-xs text-gray-500 mt-1">With promotions</p>
          </CardContent>
        </Card>
      </div>

      {/* Search */}
      <Card>
        <CardHeader>
          <div className="flex items-center gap-4">
            <div className="relative flex-1 max-w-sm">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search promotions..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Button onClick={fetchPromotions} disabled={loading}>
              Search
            </Button>
          </div>
        </CardHeader>
      </Card>

      {/* Promotions Table */}
      <Card>
        <CardHeader>
          <CardTitle>Event Promotions</CardTitle>
          <CardDescription>
            Promotional campaigns created for your events with partner businesses
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="space-y-4">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="h-16 bg-gray-100 animate-pulse rounded" />
              ))}
            </div>
          ) : promotions.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-500">No promotions found</p>
              <Button className="mt-4">
                <Plus className="mr-2 h-4 w-4" />
                Create Your First Promotion
              </Button>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Promotion</TableHead>
                  <TableHead>Partner</TableHead>
                  <TableHead>Event</TableHead>
                  <TableHead>Discount</TableHead>
                  <TableHead>Usage</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {promotions.map((promotion) => {
                  const status = getStatusBadge(promotion);
                  return (
                    <TableRow key={promotion.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{promotion.title}</div>
                          <div className="text-sm text-gray-500">{promotion.description}</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">{promotion.partner.businessName}</div>
                          <Badge variant="outline" className="text-xs">
                            {promotion.partner.partnerType}
                          </Badge>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">{promotion.event.title}</div>
                          <div className="text-sm text-gray-500">
                            {format(new Date(promotion.event.startDate), 'MMM dd, yyyy')}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <Percent className="h-4 w-4 mr-1 text-green-500" />
                          <span className="font-medium">{formatDiscount(promotion)}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          <div className="font-medium">{promotion.usageCount}</div>
                          {promotion.maxUsage && (
                            <div className="text-gray-500">of {promotion.maxUsage}</div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant={status.variant as any}>
                          {status.text}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Switch
                            checked={promotion.isActive}
                            onCheckedChange={() => togglePromotionStatus(promotion.id, promotion.isActive)}
                            className="data-[state=checked]:bg-green-500"
                          />
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setSelectedPromotion(promotion)}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Promotion Details Dialog */}
      <Dialog
        open={!!selectedPromotion}
        onOpenChange={(open) => !open && setSelectedPromotion(null)}
      >
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Promotion Details</DialogTitle>
            <DialogDescription>
              View detailed information about this promotion
            </DialogDescription>
          </DialogHeader>

          {selectedPromotion && (
            <div className="space-y-6">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium mb-2">Promotion Information</h4>
                  <div className="space-y-2 text-sm">
                    <div>
                      <span className="text-gray-500">Title:</span>
                      <span className="ml-2 font-medium">{selectedPromotion.title}</span>
                    </div>
                    <div>
                      <span className="text-gray-500">Description:</span>
                      <span className="ml-2">{selectedPromotion.description}</span>
                    </div>
                    <div>
                      <span className="text-gray-500">Discount:</span>
                      <span className="ml-2 font-medium">{formatDiscount(selectedPromotion)}</span>
                    </div>
                    <div>
                      <span className="text-gray-500">Period:</span>
                      <span className="ml-2">
                        {format(new Date(selectedPromotion.startDate), 'MMM dd')} - {format(new Date(selectedPromotion.endDate), 'MMM dd')}
                      </span>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="font-medium mb-2">Partner & Event</h4>
                  <div className="space-y-2 text-sm">
                    <div>
                      <span className="text-gray-500">Partner:</span>
                      <span className="ml-2">{selectedPromotion.partner.businessName}</span>
                    </div>
                    <div>
                      <span className="text-gray-500">Event:</span>
                      <span className="ml-2">{selectedPromotion.event.title}</span>
                    </div>
                    <div>
                      <span className="text-gray-500">Usage:</span>
                      <span className="ml-2">{selectedPromotion.usageCount}</span>
                      {selectedPromotion.maxUsage && (
                        <span className="text-gray-500"> of {selectedPromotion.maxUsage}</span>
                      )}
                    </div>
                    <div>
                      <span className="text-gray-500">Status:</span>
                      <Badge className="ml-2" variant={getStatusBadge(selectedPromotion).variant as any}>
                        {getStatusBadge(selectedPromotion).text}
                      </Badge>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex gap-3">
                <Button variant="outline" className="flex-1">
                  Edit Promotion
                </Button>
                <Button variant="outline" className="flex-1">
                  View Analytics
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
