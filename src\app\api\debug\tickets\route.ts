import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';

export const dynamic = 'force-dynamic';

/**
 * GET /api/debug/tickets
 * Debug endpoint to check if tickets exist in the database
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const eventId = searchParams.get('eventId');
    
    if (!eventId) {
      return NextResponse.json(
        { error: 'Event ID is required' },
        { status: 400 }
      );
    }
    
    // Get the event
    const event = await db.event.findUnique({
      where: { id: eventId },
      select: { 
        id: true,
        title: true,
        status: true
      }
    });
    
    if (!event) {
      return NextResponse.json(
        { error: 'Event not found' },
        { status: 404 }
      );
    }
    
    // Count tickets for the event
    const ticketCount = await db.ticket.count({
      where: { eventId }
    });
    
    // Get a sample of tickets if they exist
    const tickets = ticketCount > 0 
      ? await db.ticket.findMany({
          where: { eventId },
          take: 5,
          select: {
            id: true,
            type: true,
            price: true,
            isAvailable: true,
            quantity: true
          }
        })
      : [];
    
    return NextResponse.json({
      event,
      ticketCount,
      sampleTickets: tickets,
      message: ticketCount > 0 
        ? `Found ${ticketCount} tickets for this event` 
        : 'No tickets found for this event'
    });
  } catch (error) {
    console.error('Error debugging tickets:', error);
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
