import { NextResponse } from 'next/server'
import { db } from '@/lib/prisma'

// Mapping from interest IDs to EventCategory enum values
const interestToEventCategoryMap: Record<string, string> = {
  'music': 'MUSIC',
  'food': 'FOOD_AND_DRINK',
  'arts': 'ARTS_AND_THEATER',
  'sports': 'SPORTS_AND_FITNESS',
  'technology': 'TECHNOLOGY',
  'education': 'EDUCATIONAL_EVENTS',
  'photography': 'CULTURAL_FESTIVALS', // Photography events often fall under cultural
  'gaming': 'GAMING_EVENTS',
  'travel': 'LIFESTYLE_EVENTS', // Travel events often fall under lifestyle
  'networking': 'NETWORKING_AND_SOCIAL_GATHERINGS'
}

export async function POST(req: Request) {
  try {
    const body = await req.json()
    const { userId, interests } = body

    // Validate inputs
    if (!userId || !interests || !Array.isArray(interests)) {
      return NextResponse.json(
        { error: 'Missing required fields or invalid data format' },
        { status: 400 }
      )
    }

    // Check if user exists
    const existingUser = await db.user.findUnique({
      where: { id: userId }
    })

    if (!existingUser) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    // Map interests to event categories
    const eventCategories = interests
      .map((interest: string) => interestToEventCategoryMap[interest])
      .filter(Boolean) // Remove any undefined mappings

    // Update or create user preferences with interests stored in tags and as event categories
    const userPreferences = await db.userPreference.upsert({
      where: { userId },
      update: {
        tags: interests,
        preferredEventCategories: eventCategories,
        updatedAt: new Date()
      },
      create: {
        userId,
        tags: interests,
        preferredEventCategories: eventCategories,
        completed: false
      }
    })

    console.log(`Updated interests for user ${userId}:`, interests)
    console.log(`Mapped to event categories:`, eventCategories)

    return NextResponse.json({
      success: true,
      message: 'Interests updated successfully',
      interests: userPreferences.tags,
      eventCategories: userPreferences.preferredEventCategories
    })

  } catch (error) {
    console.error('Error updating user interests:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
