import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { getCurrentUser } from '@/lib/session';

export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  try {
    // Get the current user
    const user = await getCurrentUser();
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Get current date
    const now = new Date();
    const thirtyDaysFromNow = new Date();
    thirtyDaysFromNow.setDate(now.getDate() + 30);
    
    // Get total events count
    const totalEvents = await db.event.count({
      where: {
        userId: user.id!,
      },
    });
    
    // Get upcoming events count (events in the next 30 days)
    const upcomingEvents = await db.event.count({
      where: {
        userId: user.id!,
        startDate: {
          gte: now,
          lte: thirtyDaysFromNow,
        },
      },
    });
    
    // For now, we'll return mock data for attendees and tickets
    // In a real application, you would query the database for this information
    const totalAttendees = Math.floor(Math.random() * 1000) + 100;
    const totalTickets = Math.floor(Math.random() * 500) + 50;
    
    return NextResponse.json({
      totalEvents,
      upcomingEvents,
      totalAttendees,
      totalTickets,
    });
  } catch (error) {
    console.error('Error fetching dashboard stats:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
