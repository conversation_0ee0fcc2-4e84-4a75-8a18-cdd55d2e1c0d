'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/components/ui/use-toast';
import BusinessLogoUploader from '@/components/ui/business-logo-uploader';
import BusinessBannerUploader from '@/components/ui/business-banner-uploader';
import {
  Store,
  User,
  Phone,
  Mail,
  Globe,
  MapPin,
  Building,
  Calendar,
  Save,
  Loader2,
  ArrowLeft,
  BadgeCheck,
  AlertCircle,
  Image as ImageIcon
} from 'lucide-react';

// Form validation schema
const vendorProfileSchema = z.object({
  businessName: z.string().min(2, 'Business name is required'),
  businessType: z.string().min(1, 'Business type is required'),
  description: z.string().min(10, 'Description must be at least 10 characters'),
  phoneNumber: z.string().min(10, 'Valid phone number is required'),
  email: z.string().email('Valid email is required'),
  website: z.string().url('Valid website URL is required').optional().or(z.literal('')),
  physicalAddress: z.string().min(5, 'Physical address is required'),
  city: z.string().min(2, 'City is required'),
  province: z.string().min(2, 'Province is required'),
  postalCode: z.string().optional(),
  productCategories: z.string().min(1, 'Product categories are required'),
  serviceCategories: z.string().optional(),
  specializations: z.string().optional(),
  registrationNumber: z.string().optional(),
  taxPayerIdNumber: z.string().optional(),
  yearEstablished: z.string().optional(),
});

interface VendorProfile {
  id: string;
  businessName: string;
  businessType: string;
  description: string;
  phoneNumber: string;
  email: string;
  website?: string;
  physicalAddress: string;
  city: string;
  province: string;
  postalCode?: string;
  productCategories: string;
  serviceCategories?: string;
  specializations?: string;
  registrationNumber?: string;
  taxPayerIdNumber?: string;
  yearEstablished?: number;
  logo?: string;
  bannerImage?: string;
  verificationStatus: string;
  featured: boolean;
  createdAt: string;
  updatedAt: string;
}

export default function VendorProfilePage() {
  const router = useRouter();
  const { toast } = useToast();
  const [profile, setProfile] = useState<VendorProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [logoUrl, setLogoUrl] = useState<string>('');
  const [bannerUrl, setBannerUrl] = useState<string>('');

  const form = useForm({
    resolver: zodResolver(vendorProfileSchema),
    defaultValues: {
      businessName: '',
      businessType: '',
      description: '',
      phoneNumber: '',
      email: '',
      website: '',
      physicalAddress: '',
      city: '',
      province: '',
      postalCode: '',
      productCategories: '',
      serviceCategories: '',
      specializations: '',
      registrationNumber: '',
      taxPayerIdNumber: '',
      yearEstablished: '',
    },
  });

  useEffect(() => {
    fetchProfile();
  }, []);

  const fetchProfile = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/vendors/profile');
      
      if (!response.ok) {
        if (response.status === 404) {
          // No profile found, redirect to create profile
          router.push('/dashboard/vendor/create-profile');
          return;
        }
        throw new Error('Failed to fetch profile');
      }
      
      const data = await response.json();
      
      // Get the actual vendor profile from database
      const profileResponse = await fetch('/api/vendor/profile/details');
      if (profileResponse.ok) {
        const profileData = await profileResponse.json();
        setProfile(profileData);
        setLogoUrl(profileData.logo || '');
        setBannerUrl(profileData.bannerImage || '');

        // Set form values
        form.reset({
          businessName: profileData.businessName || '',
          businessType: profileData.businessType || '',
          description: profileData.description || '',
          phoneNumber: profileData.phoneNumber || '',
          email: profileData.email || '',
          website: profileData.website || '',
          physicalAddress: profileData.physicalAddress || '',
          city: profileData.city || '',
          province: profileData.province || '',
          postalCode: profileData.postalCode || '',
          productCategories: profileData.productCategories || '',
          serviceCategories: profileData.serviceCategories || '',
          specializations: profileData.specializations || '',
          registrationNumber: profileData.registrationNumber || '',
          taxPayerIdNumber: profileData.taxPayerIdNumber || '',
          yearEstablished: profileData.yearEstablished?.toString() || '',
        });
      } else {
        // Fallback to basic data
        setProfile({
          id: data.id,
          businessName: data.businessName,
          businessType: 'General Services',
          description: 'Professional vendor services',
          phoneNumber: '',
          email: '',
          physicalAddress: '',
          city: '',
          province: '',
          productCategories: '',
          verificationStatus: data.verificationStatus,
          featured: false,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        });
      }
    } catch (err) {
      console.error('Error fetching profile:', err);
      setError('Failed to load profile');
    } finally {
      setLoading(false);
    }
  };

  const onSubmit = async (data: any) => {
    try {
      setSaving(true);
      
      const response = await fetch('/api/vendors/profile', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...data,
          yearEstablished: data.yearEstablished ? parseInt(data.yearEstablished) : null,
          logo: logoUrl || null,
          bannerImage: bannerUrl || null,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update profile');
      }

      toast({
        title: 'Success',
        description: 'Your profile has been updated successfully.',
      });

      // Refresh profile data
      await fetchProfile();
    } catch (err) {
      console.error('Error updating profile:', err);
      toast({
        title: 'Error',
        description: 'Failed to update profile. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2">Loading profile...</span>
      </div>
    );
  }

  if (error) {
    return (
      <Card className="max-w-md mx-auto">
        <CardHeader>
          <CardTitle className="text-center text-red-500 flex items-center justify-center gap-2">
            <AlertCircle className="h-5 w-5" />
            Error
          </CardTitle>
        </CardHeader>
        <CardContent className="text-center">
          <p className="text-muted-foreground mb-4">{error}</p>
          <Button onClick={() => router.push('/dashboard/vendor')}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Dashboard
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4">
      {/* Header */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <User className="h-8 w-8" />
            Vendor Profile
          </h1>
          <p className="text-muted-foreground mt-1">
            Manage your business information and settings
          </p>
        </div>
        
        <div className="flex items-center gap-4">
          {profile && (
            <Badge 
              variant={profile.verificationStatus === 'APPROVED' ? 'default' : 'secondary'}
              className="flex items-center gap-1"
            >
              {profile.verificationStatus === 'APPROVED' && <BadgeCheck className="h-3 w-3" />}
              {profile.verificationStatus}
            </Badge>
          )}
          <Button 
            variant="outline" 
            onClick={() => router.push('/dashboard/vendor')}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Dashboard
          </Button>
        </div>
      </div>

      {/* Profile Form */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Store className="h-5 w-5" />
            Business Information
          </CardTitle>
          <CardDescription>
            Update your business details and contact information
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Business Branding */}
            <div className="space-y-6">
              <h3 className="text-lg font-medium flex items-center gap-2">
                <ImageIcon className="h-5 w-5" />
                Business Branding
              </h3>

              {/* Banner Image */}
              <div className="space-y-4">
                <BusinessBannerUploader
                  bannerUrl={bannerUrl}
                  businessName={form.watch('businessName') || 'Your Business'}
                  onBannerChange={setBannerUrl}
                  onBannerRemove={() => setBannerUrl('')}
                  disabled={saving}
                />
              </div>

              {/* Business Logo */}
              <div className="space-y-4">
                <h4 className="text-base font-medium flex items-center gap-2">
                  <Store className="h-4 w-4" />
                  Business Logo
                </h4>

                <div className="flex justify-center">
                  <BusinessLogoUploader
                    logoUrl={logoUrl}
                    businessName={form.watch('businessName') || 'Your Business'}
                    onLogoChange={setLogoUrl}
                    onLogoRemove={() => setLogoUrl('')}
                    disabled={saving}
                    size="md"
                  />
                </div>
              </div>
            </div>

            <Separator />

            {/* Basic Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium flex items-center gap-2">
                <Building className="h-5 w-5" />
                Basic Information
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="businessName">Business Name *</Label>
                  <Input
                    id="businessName"
                    {...form.register('businessName')}
                    placeholder="Enter your business name"
                  />
                  {form.formState.errors.businessName && (
                    <p className="text-sm text-red-500">{form.formState.errors.businessName.message}</p>
                  )}
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="businessType">Business Type *</Label>
                  <Input
                    id="businessType"
                    {...form.register('businessType')}
                    placeholder="e.g., Food Service, Electronics, etc."
                  />
                  {form.formState.errors.businessType && (
                    <p className="text-sm text-red-500">{form.formState.errors.businessType.message}</p>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Business Description *</Label>
                <Textarea
                  id="description"
                  {...form.register('description')}
                  placeholder="Describe your business and services"
                  rows={4}
                />
                {form.formState.errors.description && (
                  <p className="text-sm text-red-500">{form.formState.errors.description.message}</p>
                )}
              </div>
            </div>

            <Separator />

            {/* Contact Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium flex items-center gap-2">
                <Phone className="h-5 w-5" />
                Contact Information
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="phoneNumber">Phone Number *</Label>
                  <Input
                    id="phoneNumber"
                    {...form.register('phoneNumber')}
                    placeholder="+260 XX XXXXXXX"
                  />
                  {form.formState.errors.phoneNumber && (
                    <p className="text-sm text-red-500">{form.formState.errors.phoneNumber.message}</p>
                  )}
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="email">Email Address *</Label>
                  <Input
                    id="email"
                    type="email"
                    {...form.register('email')}
                    placeholder="<EMAIL>"
                  />
                  {form.formState.errors.email && (
                    <p className="text-sm text-red-500">{form.formState.errors.email.message}</p>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="website">Website</Label>
                <Input
                  id="website"
                  {...form.register('website')}
                  placeholder="https://your-website.com"
                />
                {form.formState.errors.website && (
                  <p className="text-sm text-red-500">{form.formState.errors.website.message}</p>
                )}
              </div>
            </div>

            <Separator />

            {/* Location Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium flex items-center gap-2">
                <MapPin className="h-5 w-5" />
                Location Information
              </h3>
              
              <div className="space-y-2">
                <Label htmlFor="physicalAddress">Physical Address *</Label>
                <Input
                  id="physicalAddress"
                  {...form.register('physicalAddress')}
                  placeholder="Street address"
                />
                {form.formState.errors.physicalAddress && (
                  <p className="text-sm text-red-500">{form.formState.errors.physicalAddress.message}</p>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="city">City *</Label>
                  <Input
                    id="city"
                    {...form.register('city')}
                    placeholder="City"
                  />
                  {form.formState.errors.city && (
                    <p className="text-sm text-red-500">{form.formState.errors.city.message}</p>
                  )}
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="province">Province *</Label>
                  <Input
                    id="province"
                    {...form.register('province')}
                    placeholder="Province"
                  />
                  {form.formState.errors.province && (
                    <p className="text-sm text-red-500">{form.formState.errors.province.message}</p>
                  )}
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="postalCode">Postal Code</Label>
                  <Input
                    id="postalCode"
                    {...form.register('postalCode')}
                    placeholder="Postal code"
                  />
                </div>
              </div>
            </div>

            <Separator />

            {/* Business Categories */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Business Categories</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="productCategories">Product Categories *</Label>
                  <Input
                    id="productCategories"
                    {...form.register('productCategories')}
                    placeholder="e.g., FOOD_AND_BEVERAGES, ELECTRONICS"
                  />
                  {form.formState.errors.productCategories && (
                    <p className="text-sm text-red-500">{form.formState.errors.productCategories.message}</p>
                  )}
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="serviceCategories">Service Categories</Label>
                  <Input
                    id="serviceCategories"
                    {...form.register('serviceCategories')}
                    placeholder="e.g., Catering, Delivery, Installation"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="specializations">Specializations</Label>
                <Input
                  id="specializations"
                  {...form.register('specializations')}
                  placeholder="e.g., Organic Food, Custom Electronics"
                />
              </div>
            </div>

            <Separator />

            {/* Business Registration */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Business Registration
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="registrationNumber">Registration Number</Label>
                  <Input
                    id="registrationNumber"
                    {...form.register('registrationNumber')}
                    placeholder="Business registration number"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="taxPayerIdNumber">Tax Payer ID</Label>
                  <Input
                    id="taxPayerIdNumber"
                    {...form.register('taxPayerIdNumber')}
                    placeholder="Tax payer identification number"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="yearEstablished">Year Established</Label>
                  <Input
                    id="yearEstablished"
                    type="number"
                    {...form.register('yearEstablished')}
                    placeholder="2020"
                    min="1900"
                    max={new Date().getFullYear()}
                  />
                </div>
              </div>
            </div>

            {/* Submit Button */}
            <div className="flex justify-end pt-6">
              <Button type="submit" disabled={saving} className="min-w-[120px]">
                {saving ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Save Profile
                  </>
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
