'use client';

import React, { useState, useEffect } from 'react';
import { RoleGate } from '@/components/auth/role-gate';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { CalendarDays, Ticket, Wallet, ArrowUpRight, PlusCircle, ArrowRight, Clock, CheckCircle, CreditCard, RefreshCcw, Loader2, Users, Hotel, Utensils, Wine, Music, Star, MapPin, Crown, Sparkles } from 'lucide-react';
import Link from 'next/link';
import { useCurrentUser } from '@/hooks/use-current-user';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import { toast } from '@/components/ui/use-toast';

export default function UserDashboardPage() {
  const user = useCurrentUser();

  // State for data
  const [upcomingEvents, setUpcomingEvents] = useState<any[]>([]);
  const [tickets, setTickets] = useState<any[]>([]);
  const [transactions, setTransactions] = useState<any[]>([]);
  const [partners, setPartners] = useState<any[]>([]);
  const [walletBalance, setWalletBalance] = useState<number>(0);

  // Loading states
  const [loadingEvents, setLoadingEvents] = useState<boolean>(true);
  const [loadingTickets, setLoadingTickets] = useState<boolean>(true);
  const [loadingTransactions, setLoadingTransactions] = useState<boolean>(true);
  const [loadingPartners, setLoadingPartners] = useState<boolean>(true);

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: 'numeric'
    }).format(date);
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2
    }).format(amount);
  };

  // Fetch personalized events first, then fallback
  useEffect(() => {
    const fetchEvents = async () => {
      try {
        setLoadingEvents(true);

        // Try personalized recommendations
        let response = await fetch('/api/user/recommendations');
        if (!response.ok) {
          // Fallback to generic events
          response = await fetch('/api/events?limit=10');
        }

        if (!response.ok) {
          throw new Error('Failed to fetch events');
        }

        const data = await response.json();
        const list = data.events || data || [];

        // Format the events data
        const formattedEvents = list.map((event: any) => ({
          id: event.id,
          title: event.title,
          date: event.startDate,
          location: `${event.venue || ''}, ${event.location || ''}`,
          image: event.imagePath || 'https://images.unsplash.com/photo-1501281668745-f7f57925c3b4?w=800&auto=format&fit=crop&q=60&ixlib=rb-4.0.3',
          category: event.category || 'Event',
          price: event.tickets?.[0]?.price || 0,
        }));

        setUpcomingEvents(formattedEvents);
      } catch (error) {
        console.error('Error fetching events:', error);
        toast({
          title: 'Error',
          description: 'Failed to load events',
          variant: 'destructive',
        });

        // Use fallback data if API fails
        setUpcomingEvents([
          {
            id: 'event1',
            title: 'Zambia Music Awards 2024',
            date: '2024-12-15T18:00:00Z',
            location: 'Mulungushi Conference Centre, Lusaka',
            image: 'https://images.unsplash.com/photo-1501281668745-f7f57925c3b4?w=800&auto=format&fit=crop&q=60&ixlib=rb-4.0.3',
            category: 'Music',
            price: 350.0,
          },
          {
            id: 'event2',
            title: 'Tech Innovation Summit',
            date: '2024-11-10T09:00:00Z',
            location: 'Radisson Blu Hotel, Lusaka',
            image: 'https://images.unsplash.com/photo-1540575467063-178a50c2df87?w=800&auto=format&fit=crop&q=60&ixlib=rb-4.0.3',
            category: 'Conference',
            price: 200.0,
          },
        ]);
      } finally {
        setLoadingEvents(false);
      }
    };

    fetchEvents();
  }, []);

  // Fetch user's tickets
  useEffect(() => {
    const fetchTickets = async () => {
      try {
        setLoadingTickets(true);

        // Fetch tickets from the API
        const response = await fetch('/api/user/tickets');

        if (!response.ok) {
          throw new Error('Failed to fetch tickets');
        }

        const data = await response.json();

        setTickets(data);
      } catch (error) {
        console.error('Error fetching tickets:', error);
        toast({
          title: 'Error',
          description: 'Failed to load tickets',
          variant: 'destructive',
        });

        // Use fallback data if API fails
        setTickets([
          {
            id: 'ticket1',
            eventId: 'event1',
            eventTitle: 'Zambia Music Awards 2024',
            ticketType: 'VIP Pass',
            date: '2024-12-15T18:00:00Z',
            status: 'ACTIVE',
            price: 350.00,
            purchaseDate: '2024-07-02T14:30:00Z',
            ticketNumber: 'ZMA-24-VIP-1234'
          },
          {
            id: 'ticket2',
            eventId: 'event2',
            eventTitle: 'Tech Innovation Summit',
            ticketType: 'Early Bird',
            date: '2024-11-10T09:00:00Z',
            status: 'ACTIVE',
            price: 150.00,
            purchaseDate: '2024-06-28T09:15:00Z',
            ticketNumber: 'TIS-24-EB-5678'
          }
        ]);
      } finally {
        setLoadingTickets(false);
      }
    };

    fetchTickets();
  }, []);

  // Fetch user's transactions
  useEffect(() => {
    const fetchTransactions = async () => {
      try {
        setLoadingTransactions(true);

        // Fetch transactions from the API
        const response = await fetch('/api/transactions?limit=10');

        if (!response.ok) {
          throw new Error('Failed to fetch transactions');
        }

        const data = await response.json();

        setTransactions(data.transactions || []);

        // Calculate wallet balance
        const balance = (data.transactions || []).reduce((balance: number, tx: any) => {
          if (tx.status === 'COMPLETED') {
            if (tx.type === 'TOP_UP' || tx.type === 'REFUND') {
              return balance + Number(tx.amount);
            } else if (tx.type === 'PURCHASE') {
              return balance - Number(tx.amount);
            }
          }
          return balance;
        }, 0);

        setWalletBalance(balance);
      } catch (error) {
        console.error('Error fetching transactions:', error);
        toast({
          title: 'Error',
          description: 'Failed to load transactions',
          variant: 'destructive',
        });

        // Use fallback data if API fails
        const fallbackTransactions = [
          {
            id: 'tx1',
            type: 'TOP_UP',
            amount: 500.00,
            date: '2024-07-02T14:20:00Z',
            description: 'Wallet top-up via MTN Mobile Money',
            method: 'Mobile Money',
            status: 'COMPLETED',
            reference: 'MM-24070214-8765'
          },
          {
            id: 'tx2',
            type: 'PURCHASE',
            amount: 350.00,
            date: '2024-07-02T14:30:00Z',
            description: 'Ticket purchase for Zambia Music Awards 2024',
            event: 'Zambia Music Awards 2024',
            status: 'COMPLETED',
            reference: 'PUR-24070214-1234'
          }
        ];

        setTransactions(fallbackTransactions);

        // Calculate fallback wallet balance
        const fallbackBalance = fallbackTransactions.reduce((balance, tx) => {
          if (tx.status === 'COMPLETED') {
            if (tx.type === 'TOP_UP' || tx.type === 'REFUND') {
              return balance + tx.amount;
            } else if (tx.type === 'PURCHASE') {
              return balance - tx.amount;
            }
          }
          return balance;
        }, 0);

        setWalletBalance(fallbackBalance);
      } finally {
        setLoadingTransactions(false);
      }
    };

    fetchTransactions();
  }, []);

  // Fetch partners
  useEffect(() => {
    const fetchPartners = async () => {
      try {
        setLoadingPartners(true);

        // Fetch partners from the API
        const response = await fetch('/api/partners?limit=6');

        if (!response.ok) {
          throw new Error('Failed to fetch partners');
        }

        const data = await response.json();
        setPartners(data.partners || []);
      } catch (error) {
        console.error('Error fetching partners:', error);
        toast({
          title: 'Error',
          description: 'Failed to load partners',
          variant: 'destructive',
        });

        // Use fallback data if API fails
        setPartners([
          {
            id: 'partner1',
            businessName: 'Grand Hotel Lusaka',
            partnerType: 'HOTEL',
            description: 'Luxury hotel in the heart of Lusaka with premium amenities.',
            city: 'Lusaka',
            province: 'Lusaka',
            rating: 4.5,
            totalReviews: 128,
            acceptsNfcPayments: true,
            isVerified: true,
            featured: true
          },
          {
            id: 'partner2',
            businessName: 'Savanna Restaurant',
            partnerType: 'RESTAURANT',
            description: 'Fine dining restaurant serving local and international cuisine.',
            city: 'Lusaka',
            province: 'Lusaka',
            rating: 4.2,
            totalReviews: 89,
            acceptsNfcPayments: true,
            isVerified: true,
            featured: false
          }
        ]);
      } finally {
        setLoadingPartners(false);
      }
    };

    fetchPartners();
  }, []);

  // Get status badge color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'USED':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
      case 'CANCELLED':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      case 'COMPLETED':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'FAILED':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      default:
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
    }
  };

  // Get transaction icon
  const getTransactionIcon = (type: string) => {
    switch (type) {
      case 'TOP_UP':
        return <PlusCircle className="h-4 w-4 text-green-600 dark:text-green-400" />;
      case 'PURCHASE':
        return <Ticket className="h-4 w-4 text-red-600 dark:text-red-400" />;
      case 'REFUND':
        return <RefreshCcw className="h-4 w-4 text-blue-600 dark:text-blue-400" />;
      default:
        return <CreditCard className="h-4 w-4 text-gray-600 dark:text-gray-400" />;
    }
  };

  // Get partner type icon
  const getPartnerIcon = (type: string) => {
    switch (type) {
      case 'HOTEL':
        return <Hotel className="h-4 w-4" />;
      case 'RESTAURANT':
        return <Utensils className="h-4 w-4" />;
      case 'BAR':
        return <Wine className="h-4 w-4" />;
      case 'NIGHTCLUB':
        return <Music className="h-4 w-4" />;
      default:
        return <Utensils className="h-4 w-4" />;
    }
  };

  // Get partner type badge variant
  const getTypeBadge = (type: string) => {
    const variants = {
      'HOTEL': 'default',
      'RESTAURANT': 'secondary',
      'BAR': 'outline',
      'NIGHTCLUB': 'destructive'
    };
    return variants[type as keyof typeof variants] || 'secondary';
  };

  return (
    <RoleGate allowedRole="USER">
      <div className="p-4 sm:p-6 max-w-7xl mx-auto">
        {/* Welcome Section */}
        <div className="mb-8">
          <h1 className="text-2xl sm:text-3xl font-bold tracking-tight">Welcome back, {user?.name?.split(" ")[0] || "User"}</h1>
          <p className="text-gray-500 mt-1">Here&apos;s what&apos;s happening with your events and tickets.</p>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex justify-between items-start">
                <div>
                  <p className="text-sm font-medium text-gray-500">Upcoming Events</p>
                  {loadingEvents ? (
                    <div className="flex items-center mt-1">
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      <span className="text-sm">Loading...</span>
                    </div>
                  ) : (
                    <h3 className="text-3xl font-bold mt-1">{upcomingEvents.length}</h3>
                  )}
                  <p className="text-sm text-blue-600 mt-1 flex items-center">
                    <ArrowUpRight className="h-4 w-4 mr-1" />
                    <span>View all events</span>
                  </p>
                </div>
                <div className="bg-blue-100 p-3 rounded-full">
                  <CalendarDays className="h-6 w-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex justify-between items-start">
                <div>
                  <p className="text-sm font-medium text-gray-500">Active Tickets</p>
                  {loadingTickets ? (
                    <div className="flex items-center mt-1">
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      <span className="text-sm">Loading...</span>
                    </div>
                  ) : (
                    <h3 className="text-3xl font-bold mt-1">{tickets.filter(t => t.status === 'ACTIVE' || t.status === 'UPCOMING').length}</h3>
                  )}
                  <p className="text-sm text-purple-600 mt-1 flex items-center">
                    <ArrowUpRight className="h-4 w-4 mr-1" />
                    <span>View all tickets</span>
                  </p>
                </div>
                <div className="bg-purple-100 p-3 rounded-full">
                  <Ticket className="h-6 w-6 text-purple-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex justify-between items-start">
                <div>
                  <p className="text-sm font-medium text-gray-500">Wallet Balance</p>
                  {loadingTransactions ? (
                    <div className="flex items-center mt-1">
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      <span className="text-sm">Loading...</span>
                    </div>
                  ) : (
                    <h3 className="text-3xl font-bold mt-1">{formatCurrency(walletBalance)}</h3>
                  )}
                  <p className="text-sm text-green-600 mt-1 flex items-center">
                    <ArrowUpRight className="h-4 w-4 mr-1" />
                    <span>Top up balance</span>
                  </p>
                </div>
                <div className="bg-green-100 p-3 rounded-full">
                  <Wallet className="h-6 w-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Elite Communication Upgrade Banner */}
        <Card className="bg-gradient-to-r from-purple-600 to-blue-600 text-white">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="p-3 bg-white/20 rounded-full">
                  <Crown className="h-6 w-6" />
                </div>
                <div>
                  <h3 className="text-xl font-bold">Unlock Elite Networking</h3>
                  <p className="text-purple-100">Connect with professionals, access exclusive chat rooms, and schedule meetings</p>
                </div>
              </div>
              <Button
                asChild
                variant="secondary"
                className="bg-white text-purple-600 hover:bg-gray-100"
              >
                <Link href="/dashboard/user/upgrade">
                  <Sparkles className="h-4 w-4 mr-2" />
                  Upgrade Now
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>

        <Tabs defaultValue="events" className="space-y-6">
          <TabsList>
            <TabsTrigger value="events">My Events</TabsTrigger>
            <TabsTrigger value="tickets">My Tickets</TabsTrigger>
            <TabsTrigger value="partners">Partners</TabsTrigger>
            <TabsTrigger value="wallet">Wallet</TabsTrigger>
            <TabsTrigger value="elite">Elite Features</TabsTrigger>
          </TabsList>

          {/* Events Tab */}
          <TabsContent value="events" className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-bold">Upcoming Events</h2>
              <Button asChild>
                <Link href="/dashboard/user/browse-events">
                  <PlusCircle className="h-4 w-4 mr-2" />
                  Browse Events
                </Link>
              </Button>
            </div>

            {loadingEvents ? (
              <Card>
                <CardContent className="p-8 text-center">
                  <div className="flex flex-col items-center justify-center py-8">
                    <Loader2 className="h-12 w-12 text-blue-600 animate-spin mb-4" />
                    <h3 className="text-lg font-medium">Loading events...</h3>
                    <p className="text-gray-500 dark:text-gray-400 mt-2">Please wait while we fetch your upcoming events.</p>
                  </div>
                </CardContent>
              </Card>
            ) : upcomingEvents.length === 0 ? (
              <Card>
                <CardContent className="p-8 text-center">
                  <div className="mx-auto w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-4">
                    <CalendarDays className="h-8 w-8 text-gray-500" />
                  </div>
                  <h3 className="text-lg font-medium mb-2">No upcoming events</h3>
                  <p className="text-gray-500 dark:text-gray-400 mb-4">You don&apos;t have any upcoming events. Browse events to find something you&apos;re interested in.</p>
                  <Button asChild>
                    <Link href="/dashboard/user/browse-events">Browse Events</Link>
                  </Button>
                </CardContent>
              </Card>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {upcomingEvents.map((event, index) => (
                  <Card key={index} className="overflow-hidden">
                    <div className="h-40 relative">
                      <img
                        src={event.image}
                        alt={event.title}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <CardContent className="p-6">
                      <h3 className="text-lg font-bold mb-2">{event.title}</h3>
                      <div className="flex items-center text-gray-500 dark:text-gray-400 mb-1">
                        <Clock className="h-4 w-4 mr-2" />
                        <span className="text-sm">{formatDate(event.date)}</span>
                      </div>
                      <div className="flex items-start text-gray-500 dark:text-gray-400 mb-4">
                        <svg className="h-4 w-4 mr-2 mt-0.5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                        <span className="text-sm">{event.location}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <Button variant="outline" asChild>
                          <Link href={`/events/${event.id}`}>
                            View Details
                          </Link>
                        </Button>
                        <Button asChild>
                          <Link href={`/events/${event.id}/tickets`}>
                            View Tickets
                          </Link>
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}

            <div className="text-center mt-4">
              <Button variant="outline" asChild>
                <Link href="/dashboard/user/events">
                  View All Events
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </div>
          </TabsContent>

          {/* Tickets Tab */}
          <TabsContent value="tickets" className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-bold">Your Tickets</h2>
              <div className="space-x-2">
                <Badge variant="outline">All</Badge>
                <Badge variant="secondary">Active</Badge>
                <Badge variant="outline">Used</Badge>
                <Badge variant="outline">Cancelled</Badge>
              </div>
            </div>

            {loadingTickets ? (
              <Card>
                <CardContent className="p-8 text-center">
                  <div className="flex flex-col items-center justify-center py-8">
                    <Loader2 className="h-12 w-12 text-purple-600 animate-spin mb-4" />
                    <h3 className="text-lg font-medium">Loading tickets...</h3>
                    <p className="text-gray-500 dark:text-gray-400 mt-2">Please wait while we fetch your tickets.</p>
                  </div>
                </CardContent>
              </Card>
            ) : tickets.length === 0 ? (
              <Card>
                <CardContent className="p-8 text-center">
                  <div className="mx-auto w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-4">
                    <Ticket className="h-8 w-8 text-gray-500" />
                  </div>
                  <h3 className="text-lg font-medium mb-2">No tickets found</h3>
                  <p className="text-gray-500 dark:text-gray-400 mb-4">You don&apos;t have any tickets yet. Browse events to purchase tickets.</p>
                  <Button asChild>
                    <Link href="/dashboard/user/browse-events">Browse Events</Link>
                  </Button>
                </CardContent>
              </Card>
            ) : (
              <Card>
                <CardContent className="p-0">
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="border-b dark:border-gray-700">
                          <th className="text-left py-3 px-4">Event</th>
                          <th className="text-left py-3 px-4">Ticket Type</th>
                          <th className="text-left py-3 px-4">Date</th>
                          <th className="text-left py-3 px-4">Status</th>
                          <th className="text-right py-3 px-4">Price</th>
                          <th className="text-right py-3 px-4">Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {tickets.map((ticket, index) => (
                          <tr key={index} className="border-b dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800">
                            <td className="py-3 px-4 font-medium">{ticket.eventTitle}</td>
                            <td className="py-3 px-4">{ticket.ticketType}</td>
                            <td className="py-3 px-4">{formatDate(ticket.date || ticket.eventDate)}</td>
                            <td className="py-3 px-4">
                              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(ticket.status)}`}>
                                {ticket.status}
                              </span>
                            </td>
                            <td className="py-3 px-4 text-right">{formatCurrency(Number(ticket.price || ticket.ticketPrice || 0))}</td>
                            <td className="py-3 px-4 text-right">
                              <Button variant="outline" size="sm" asChild>
                                <Link href={`/dashboard/user/tickets/${ticket.id}`}>
                                  View
                                </Link>
                              </Button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </CardContent>
              </Card>
            )}

            <div className="text-center mt-4">
              <Button variant="outline" asChild>
                <Link href="/dashboard/user/tickets">
                  View All Tickets
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </div>
          </TabsContent>

          {/* Partners Tab */}
          <TabsContent value="partners" className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-bold">Event Partners</h2>
              <Button variant="outline" asChild>
                <Link href="/dashboard/user/partners">
                  <Users className="h-4 w-4 mr-2" />
                  View All Partners
                </Link>
              </Button>
            </div>

            {loadingPartners ? (
              <Card>
                <CardContent className="p-8 text-center">
                  <div className="flex flex-col items-center justify-center py-8">
                    <Loader2 className="h-12 w-12 text-blue-600 animate-spin mb-4" />
                    <h3 className="text-lg font-medium">Loading partners...</h3>
                    <p className="text-gray-500 dark:text-gray-400 mt-2">Please wait while we fetch available partners.</p>
                  </div>
                </CardContent>
              </Card>
            ) : partners.length === 0 ? (
              <Card>
                <CardContent className="p-8 text-center">
                  <div className="mx-auto w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-4">
                    <Users className="h-8 w-8 text-gray-500" />
                  </div>
                  <h3 className="text-lg font-medium mb-2">No partners available</h3>
                  <p className="text-gray-500 dark:text-gray-400 mb-4">
                    No verified partners are currently available. Check back later for new partners.
                  </p>
                  <Button asChild>
                    <Link href="/dashboard/user/partners">Browse Partners</Link>
                  </Button>
                </CardContent>
              </Card>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {partners.slice(0, 6).map((partner, index) => (
                  <Card key={index} className="hover:shadow-lg transition-shadow">
                    <CardHeader>
                      <div className="flex items-start justify-between">
                        <div className="flex items-center gap-2">
                          {getPartnerIcon(partner.partnerType)}
                          <CardTitle className="text-lg">{partner.businessName}</CardTitle>
                        </div>
                        <div className="flex gap-1">
                          {partner.isVerified && (
                            <Badge variant="outline" className="bg-green-100 text-green-800 text-xs">
                              Verified
                            </Badge>
                          )}
                          {partner.featured && (
                            <Badge variant="outline" className="bg-blue-100 text-blue-800 text-xs">
                              Featured
                            </Badge>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center gap-4 text-sm text-gray-500">
                        <Badge variant={getTypeBadge(partner.partnerType) as any}>
                          {partner.partnerType}
                        </Badge>
                        <div className="flex items-center gap-1">
                          <Star className="h-4 w-4 text-yellow-400" />
                          <span>{partner.rating}</span>
                          <span>({partner.totalReviews})</span>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-gray-600 mb-3 line-clamp-2">{partner.description}</p>

                      <div className="flex items-center justify-between text-sm mb-3">
                        <div className="flex items-center gap-1 text-gray-500">
                          <MapPin className="h-4 w-4" />
                          <span>{partner.city}, {partner.province}</span>
                        </div>
                      </div>

                      <div className="flex items-center gap-2 mb-3">
                        {partner.acceptsNfcPayments && (
                          <Badge variant="outline" className="text-xs">
                            NFC Payments
                          </Badge>
                        )}
                      </div>

                      <div className="flex gap-2">
                        <Button size="sm" variant="outline" className="flex-1">
                          View Details
                        </Button>
                        <Button size="sm" className="flex-1">
                          Contact
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}

            <div className="text-center mt-4">
              <Button variant="outline" asChild>
                <Link href="/dashboard/user/partners">
                  View All Partners
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </div>
          </TabsContent>

          {/* Wallet Tab */}
          <TabsContent value="wallet" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Card className="md:col-span-2 bg-gradient-to-br from-green-500 to-green-600 text-white">
                <CardContent className="p-6">
                  <div className="flex flex-col">
                    <p className="text-green-100 mb-1">Current Balance</p>
                    {loadingTransactions ? (
                      <div className="flex items-center mt-1 mb-4">
                        <Loader2 className="h-6 w-6 mr-2 animate-spin text-white" />
                        <span className="text-white">Loading balance...</span>
                      </div>
                    ) : (
                      <h3 className="text-3xl font-bold mb-4">{formatCurrency(walletBalance)}</h3>
                    )}
                    <div className="flex gap-3 mt-2">
                      <Button variant="secondary" className="bg-white text-green-600 hover:bg-green-50" asChild>
                        <Link href="/dashboard/user/wallet/topup">
                          Top Up Balance
                          <PlusCircle className="ml-2 h-4 w-4" />
                        </Link>
                      </Button>
                      <Button variant="outline" className="text-white border-white hover:bg-green-600" asChild>
                        <Link href="/dashboard/user/wallet/history">
                          Transaction History
                        </Link>
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Quick Actions</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <Button className="w-full justify-start" variant="outline" asChild>
                    <Link href="/dashboard/user/wallet/topup">
                      <PlusCircle className="mr-2 h-4 w-4" />
                      Top Up Balance
                    </Link>
                  </Button>
                  <Button className="w-full justify-start" variant="outline" asChild>
                    <Link href="/dashboard/user/wallet/history">
                      <Clock className="mr-2 h-4 w-4" />
                      Transaction History
                    </Link>
                  </Button>
                  <Button className="w-full justify-start" variant="outline" asChild>
                    <Link href="/dashboard/user/wallet/payment-methods">
                      <CreditCard className="mr-2 h-4 w-4" />
                      Payment Methods
                    </Link>
                  </Button>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Recent Transactions</CardTitle>
                <CardDescription>Your recent wallet activity</CardDescription>
              </CardHeader>
              <CardContent>
                {loadingTransactions ? (
                  <div className="flex flex-col items-center justify-center py-8">
                    <Loader2 className="h-8 w-8 text-green-600 animate-spin mb-4" />
                    <p className="text-gray-500">Loading transactions...</p>
                  </div>
                ) : transactions.length === 0 ? (
                  <div className="text-center py-8">
                    <p className="text-gray-500">No transactions found</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {transactions.slice(0, 4).map((transaction, index) => (
                      <div key={index} className="flex justify-between items-center py-2 border-b dark:border-gray-700">
                        <div className="flex items-center">
                          <div className={`p-2 rounded-full mr-3 ${
                            transaction.type === 'TOP_UP' ? 'bg-green-100 dark:bg-green-900' :
                            transaction.type === 'PURCHASE' ? 'bg-red-100 dark:bg-red-900' :
                            'bg-blue-100 dark:bg-blue-900'
                          }`}>
                            {getTransactionIcon(transaction.type)}
                          </div>
                          <div>
                            <p className="font-medium">{transaction.description}</p>
                            <p className="text-sm text-gray-500 dark:text-gray-400">{formatDate(transaction.date || transaction.createdAt)}</p>
                          </div>
                        </div>
                        <p className={`font-medium ${
                          transaction.type === 'PURCHASE' ? 'text-red-600' : 'text-green-600'
                        }`}>
                          {transaction.type === 'PURCHASE' ? '-' : '+'}{formatCurrency(Number(transaction.amount))}
                        </p>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
              <CardFooter>
                <Button variant="outline" className="w-full" asChild>
                  <Link href="/dashboard/user/wallet/history">
                    View All Transactions
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>

          {/* Elite Features Tab */}
          <TabsContent value="elite" className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-bold">Elite Communication Features</h2>
              <Button asChild>
                <Link href="/dashboard/user/upgrade">
                  <Crown className="h-4 w-4 mr-2" />
                  Upgrade Plan
                </Link>
              </Button>
            </div>

            {/* Elite Status Card */}
            <Card className="bg-gradient-to-r from-purple-50 to-blue-50 border-purple-200">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="p-3 bg-purple-100 rounded-full">
                      <Crown className="h-6 w-6 text-purple-600" />
                    </div>
                    <div>
                      <h3 className="text-lg font-bold text-purple-900">Current Plan: Basic</h3>
                      <p className="text-purple-700">Upgrade to unlock premium networking features</p>
                    </div>
                  </div>
                  <Button asChild className="bg-purple-600 hover:bg-purple-700">
                    <Link href="/dashboard/user/upgrade">
                      <Sparkles className="h-4 w-4 mr-2" />
                      Upgrade Now
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Feature Preview Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Users className="h-5 w-5 mr-2 text-blue-600" />
                    Attendee Directory
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 mb-4">Connect with other event attendees and build your professional network.</p>
                  <div className="space-y-2">
                    <div className="flex items-center text-sm">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                      View attendee profiles
                    </div>
                    <div className="flex items-center text-sm text-gray-400">
                      <Clock className="h-4 w-4 mr-2" />
                      Direct messaging (Elite+)
                    </div>
                    <div className="flex items-center text-sm text-gray-400">
                      <Clock className="h-4 w-4 mr-2" />
                      Meeting scheduling (Elite Pro)
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Star className="h-5 w-5 mr-2 text-yellow-600" />
                    Exclusive Chat Rooms
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 mb-4">Join exclusive chat rooms for premium networking opportunities.</p>
                  <div className="space-y-2">
                    <div className="flex items-center text-sm text-gray-400">
                      <Clock className="h-4 w-4 mr-2" />
                      General chat rooms (Elite+)
                    </div>
                    <div className="flex items-center text-sm text-gray-400">
                      <Clock className="h-4 w-4 mr-2" />
                      Elite Pro exclusive rooms
                    </div>
                    <div className="flex items-center text-sm text-gray-400">
                      <Clock className="h-4 w-4 mr-2" />
                      File sharing capabilities
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Pricing Preview */}
            <Card>
              <CardHeader>
                <CardTitle>Elite Communication Pricing</CardTitle>
                <CardDescription>Choose the plan that fits your networking needs</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-semibold">Elite</h4>
                      <Badge className="bg-purple-100 text-purple-800">Popular</Badge>
                    </div>
                    <p className="text-2xl font-bold mb-2">$29.99</p>
                    <p className="text-sm text-gray-600 mb-4">per event</p>
                    <ul className="space-y-1 text-sm">
                      <li>✓ Direct messaging</li>
                      <li>✓ Contact information access</li>
                      <li>✓ File sharing (10MB)</li>
                    </ul>
                  </div>
                  <div className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-semibold">Elite Pro</h4>
                      <Badge className="bg-yellow-100 text-yellow-800">Premium</Badge>
                    </div>
                    <p className="text-2xl font-bold mb-2">$59.99</p>
                    <p className="text-sm text-gray-600 mb-4">per event</p>
                    <ul className="space-y-1 text-sm">
                      <li>✓ Everything in Elite</li>
                      <li>✓ Meeting scheduling</li>
                      <li>✓ Exclusive chat rooms</li>
                      <li>✓ Video call integration</li>
                    </ul>
                  </div>
                </div>
                <div className="mt-6 text-center">
                  <Button asChild size="lg" className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700">
                    <Link href="/dashboard/user/upgrade">
                      <Crown className="h-4 w-4 mr-2" />
                      View All Plans & Upgrade
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </RoleGate>
  );
}
