import { NextRequest } from 'next/server';
import { db } from '@/lib/prisma';
import { withMonetization, createApiResponse } from '@/lib/api-monetization';
import { corsPreflightResponse } from '@/lib/cors';

export const dynamic = 'force-dynamic';

/**
 * GET /api/monetized/analytics/demographics
 * Get audience demographics data
 */
export const GET = withMonetization(async (request: NextRequest, context: any, apiKeyData: any) => {
  try {
    const { searchParams } = new URL(request.url);
    const eventId = searchParams.get('eventId');
    const category = searchParams.get('category');

    // Build the where clause
    const where: any = {};

    if (eventId) {
      where.eventId = eventId;
    } else if (category) {
      where.event = {
        category,
      };
    }

    // Get audience data
    const audienceData = await db.audienceData.findMany({
      where,
      include: {
        event: {
          select: {
            id: true,
            title: true,
            category: true,
          },
        },
      },
    });

    // Group by age
    const ageGroups: Record<string, { percentage: number; engagement: number; count: number }> = {};

    audienceData.forEach(data => {
      if (!ageGroups[data.age]) {
        ageGroups[data.age] = { percentage: 0, engagement: 0, count: 0 };
      }

      ageGroups[data.age].percentage += data.percentage;
      ageGroups[data.age].engagement += data.engagement;
      ageGroups[data.age].count += 1;
    });

    // Calculate averages
    Object.keys(ageGroups).forEach(age => {
      const group = ageGroups[age];
      group.percentage = parseFloat((group.percentage / group.count).toFixed(2));
      group.engagement = parseFloat((group.engagement / group.count).toFixed(2));
    });

    // Get locations from events
    const events = await db.event.findMany({
      where: category ? { category: category as any } : {},
      select: {
        location: true,
      },
    });

    // Group by location
    const locationCounts: Record<string, number> = {};
    let totalLocations = 0;

    events.forEach(event => {
      const location = event.location.split(',')[0].trim(); // Get city/region
      locationCounts[location] = (locationCounts[location] || 0) + 1;
      totalLocations += 1;
    });

    // Calculate percentages
    const locations = Object.entries(locationCounts).map(([location, count]) => ({
      location,
      percentage: parseFloat(((count / totalLocations) * 100).toFixed(2)),
    }));

    // Sort by percentage (highest first)
    locations.sort((a, b) => b.percentage - a.percentage);

    // Format the response
    const formattedAgeGroups = Object.entries(ageGroups).map(([range, data]) => ({
      range,
      percentage: data.percentage,
      engagement: data.engagement,
    }));

    return createApiResponse(
      {
        demographics: {
          ageGroups: formattedAgeGroups,
          locations: locations.slice(0, 10), // Top 10 locations
        },
        filters: {
          eventId,
          category,
        },
      },
      200,
      apiKeyData.apiKey.id,
      request.nextUrl.pathname,
      request.method
    );
  } catch (error) {
    console.error('Error fetching demographics data:', error);

    return createApiResponse(
      { error: 'Internal server error' },
      500,
      apiKeyData.apiKey.id,
      request.nextUrl.pathname,
      request.method
    );
  }
}, ['read:analytics']);

/**
 * OPTIONS /api/monetized/analytics/demographics
 * Handle CORS preflight requests
 */
export async function OPTIONS(request: NextRequest) {
  return corsPreflightResponse('GET, OPTIONS', request);
}
