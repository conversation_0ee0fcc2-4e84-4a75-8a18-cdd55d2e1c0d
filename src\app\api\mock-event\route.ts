import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient, EventStatus } from '@prisma/client';
const prisma = new PrismaClient();

export async function GET(request: NextRequest) {
  try {
    // Check if the event already exists
    const existingEvent = await prisma.event.findUnique({
      where: {
        id: '1',
      },
    });

    if (existingEvent) {
      return NextResponse.json({ message: 'Mock event already exists', event: existingEvent });
    }

    // Create a mock user if needed
    let user = await prisma.user.findFirst({
      where: {
        email: '<EMAIL>',
      },
    });

    if (!user) {
      user = await prisma.user.create({
        data: {
          name: 'Mock User',
          email: '<EMAIL>',
          role: 'ORGANIZER',
        },
      });
    }

    // Create a mock event
    const mockEvent = await prisma.event.create({
      data: {
        id: '1',
        title: 'Mock Event',
        description: 'This is a mock event for testing purposes',
        eventType: 'PHYSICAL',
        category: 'CONFERENCES_AND_WORKSHOPS',
        startDate: new Date(),
        endDate: new Date(Date.now() + 86400000), // 1 day from now
        startTime: '10:00 AM',
        endTime: '4:00 PM',
        location: 'Mock Location',
        venue: 'Mock Venue',
        status: EventStatus.Published,
        userId: user.id!,
        imagePath: 'https://images.unsplash.com/photo-1501281668745-f7f57925c3b4',
      },
    });

    // Create mock tickets for the event
    const mockTickets = await prisma.ticket.createMany({
      data: [
        {
          eventId: mockEvent.id,
          type: 'REGULAR',
          price: 99.99,
          isAvailable: true,
          totalSeats: 100,
          orderId: 'mock-order-1',
          userId: 'mock-user-1',
          email: '<EMAIL>',
          saleStartTime: new Date().toISOString(),
          saleEndTime: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days from now
          quantity: 1,
          totalPrice: 99.99,
          regularSeats: 1,
          vipSeats: 0,
          vvipSeats: 0,
          regularPrice: 99.99,
          vipPrice: 0,
          specialGuestType: '',
          specialGuestName: '',
          qrCodeData: 'mock-qr-code-1',
        },
        {
          eventId: mockEvent.id,
          type: 'VIP',
          price: 199.99,
          isAvailable: true,
          totalSeats: 50,
          orderId: 'mock-order-2',
          userId: 'mock-user-2',
          email: '<EMAIL>',
          saleStartTime: new Date().toISOString(),
          saleEndTime: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days from now
          quantity: 1,
          totalPrice: 199.99,
          regularSeats: 0,
          vipSeats: 1,
          vvipSeats: 0,
          regularPrice: 0,
          vipPrice: 199.99,
          specialGuestType: '',
          specialGuestName: '',
          qrCodeData: 'mock-qr-code-2',
        },
      ],
    });

    return NextResponse.json({ message: 'Mock event created successfully', event: mockEvent });
  } catch (error) {
    console.error('Error creating mock event:', error);
    return NextResponse.json({ error: 'Failed to create mock event' }, { status: 500 });
  }
}
