// Import necessary libraries and Prisma client
import {db } from '@/lib/prisma'; // Adjust based on your Prisma client path
import { NextRequest, NextResponse } from 'next/server';

export async function POST(req: NextRequest) {
  try {
    const { userId, amount } = await req.json();

    // Validate the input
    if (!userId || !amount || amount <= 0) {
      return NextResponse.json({ error: 'Invalid userId or amount' }, { status: 400 });
    }

    // Execute transaction to ensure atomicity
    const result = await db.$transaction(async (prisma) => {
      // Update user's account balance
      const updatedUser = await prisma.user.update({
        where: { id: userId },
        data: {
          accountBalance: { increment: amount },
        },
      });

      // Record the transaction in FinancialTransaction table
      const transaction = await prisma.financialTransaction.create({
        data: {
          userId,
          amount,
          type: 'DEPOSIT',
          description: 'Funds deposited by user',
        },
      });

      return { updatedUser, transaction };
    });

    return NextResponse.json({ message: 'Deposit successful', result }, { status: 200 });
  } catch (error) {
    console.error(error);
    return NextResponse.json({ error: 'An error occurred during deposit' }, { status: 500 });
  }
}
