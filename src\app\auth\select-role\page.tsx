'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { SimpleRoleSelection } from '@/components/auth/simple-role-selection';
import { FormError } from '@/components/form-error';
import { FormSuccess } from '@/components/form-success';
import { UserRole } from '@prisma/client';
import { Users, Calendar, Store, Building, Code, Sparkles } from 'lucide-react';

// Role definitions with descriptions and icons
const roleDefinitions = {
  USER: {
    title: 'Event Attendee',
    description: 'Browse, discover, and attend amazing events',
    details: 'Perfect for individuals who want to find and attend events, connect with others, and explore new experiences.',
    icon: Users,
    color: 'bg-blue-50 border-blue-200 hover:bg-blue-100',
    selectedColor: 'border-blue-500 bg-blue-50'
  },
  ORGANIZER: {
    title: 'Event Organizer',
    description: 'Create and manage events for your community',
    details: 'Ideal for individuals or organizations who want to create, promote, and manage events of all sizes.',
    icon: Calendar,
    color: 'bg-green-50 border-green-200 hover:bg-green-100',
    selectedColor: 'border-green-500 bg-green-50'
  },
  VENDOR: {
    title: 'Vendor/Seller',
    description: 'Sell products and services at events',
    details: 'Great for businesses and individuals who want to sell products, services, or merchandise at events.',
    icon: Store,
    color: 'bg-purple-50 border-purple-200 hover:bg-purple-100',
    selectedColor: 'border-purple-500 bg-purple-50'
  },
  PARTNER: {
    title: 'Venue Partner',
    description: 'Provide venues and hospitality services',
    details: 'Perfect for venues, hotels, restaurants, and hospitality providers who want to partner with event organizers.',
    icon: Building,
    color: 'bg-orange-50 border-orange-200 hover:bg-orange-100',
    selectedColor: 'border-orange-500 bg-orange-50'
  },
  DEVELOPER: {
    title: 'Developer',
    description: 'Access developer tools and APIs',
    details: 'For developers who want to integrate with our platform, build applications, or access our APIs.',
    icon: Code,
    color: 'bg-gray-50 border-gray-200 hover:bg-gray-100',
    selectedColor: 'border-gray-500 bg-gray-50'
  }
};

export default function SelectRolePage() {
  const router = useRouter();
  const { data: session, status } = useSession();
  const searchParams = useSearchParams();
  const isPartner = searchParams?.get('partner') === 'true';
  const isFirstTime = searchParams?.get('firstTime') === 'true';

  const [selectedRole, setSelectedRole] = useState<UserRole>(
    isPartner ? UserRole.PARTNER : UserRole.USER
  );
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Redirect if not authenticated
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/login');
    }
  }, [status, router]);

  const handleSubmit = async () => {
    if (!session?.user?.id) {
      setError('User ID is missing. Please try signing in again.');
      return;
    }

    setIsLoading(true);
    setError(null);
    setSuccess(null);

    try {
      const response = await fetch('/api/auth/update-role', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: session.user.id,
          role: selectedRole,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        setError(data.error || 'Failed to update role');
      } else {
        setSuccess('Role updated successfully!');

        // Set the user-role cookie
        try {
          await fetch('/api/auth/set-role-cookie');
          console.log("Set user-role cookie after role selection");
        } catch (error) {
          console.error("Error setting user-role cookie after role selection:", error);
        }

        // Redirect to interests selection after a short delay
        setTimeout(() => {
          router.push(`/auth/select-interests?role=${selectedRole}&firstTime=${isFirstTime}`);
        }, 900);
      }
    } catch (err) {
      setError('An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  // Show loading state while session is loading
  if (status === 'loading') {
    return (
      <div className="flex justify-center items-center min-h-screen bg-gray-50">
        <Card className="w-full max-w-md shadow-lg p-8 text-center">
          <p>Loading...</p>
        </Card>
      </div>
    );
  }

  return (
    <div className="flex justify-center items-center min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <Card className="w-full max-w-5xl shadow-xl">
        <CardHeader className="space-y-2 px-6 py-4 text-center">
          <div className="flex justify-center mb-2">
            <div className="p-2 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full">
              <Sparkles className="h-6 w-6 text-white" />
            </div>
          </div>
          <CardTitle className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
            {isFirstTime ? 'Welcome! Let\'s Get Started' : 'Select Your Role'}
          </CardTitle>
          <CardDescription className="text-base text-gray-600 max-w-2xl mx-auto">
            {session?.user?.email ? `Welcome, ${session.user.email}! ` : ''}
            {isFirstTime
              ? 'Choose your role to personalize your experience and unlock the features that matter most to you.'
              : 'Please select your role to complete your account setup.'
            }
          </CardDescription>
          {isFirstTime && (
            <Badge variant="secondary" className="mx-auto">
              Step 1 of 3
            </Badge>
          )}
        </CardHeader>

        <CardContent className="px-6 pb-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 mb-4">
            {Object.entries(roleDefinitions).map(([role, def]) => {
              const Icon = def.icon;
              const isSelected = selectedRole === role;

              return (
                <button
                  key={role}
                  type="button"
                  onClick={() => setSelectedRole(role as UserRole)}
                  className={`p-4 border-2 rounded-lg text-left transition-all duration-200 ${
                    isSelected ? def.selectedColor : def.color
                  } ${isSelected ? 'ring-2 ring-offset-2 ring-blue-500' : ''}`}
                >
                  <div className="flex items-start space-x-3">
                    <div className={`p-1.5 rounded-lg ${isSelected ? 'bg-white' : 'bg-white/50'}`}>
                      <Icon className="h-5 w-5 text-gray-700" />
                    </div>
                    <div className="flex-1">
                      <h3 className="font-semibold text-gray-900 mb-1 text-sm">{def.title}</h3>
                      <p className="text-xs text-gray-600 mb-1">{def.description}</p>
                      <p className="text-xs text-gray-500 leading-tight">{def.details}</p>
                    </div>
                  </div>
                </button>
              );
            })}
          </div>

          {error && <FormError message={error} className="mt-4" />}
          {success && <FormSuccess message={success} className="mt-4" />}
        </CardContent>

        <CardFooter className="flex flex-col sm:flex-row gap-3 justify-between px-6 py-4 bg-gray-50 rounded-b-lg">
          <Button
            variant="outline"
            onClick={() => router.push('/auth/login')}
            disabled={isLoading}
            className="w-full sm:w-auto"
          >
            Back to Login
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={isLoading}
            className="w-full sm:w-auto bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700"
          >
            {isLoading ? 'Setting up your account...' : isFirstTime ? 'Continue to Interests' : 'Continue'}
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
