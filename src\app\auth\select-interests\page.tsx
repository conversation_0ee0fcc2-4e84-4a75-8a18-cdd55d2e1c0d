"use client";

import { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { useSession } from 'next-auth/react'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Check } from 'lucide-react'
import { Badge } from '@/components/ui/badge'
import { toast } from 'sonner'
import { FormError } from '@/components/form-error'
import { FormSuccess } from '@/components/form-success'
import { Heart, Music, Camera, Utensils, Gamepad2, Briefcase, GraduationCap, Palette, Plane, Users, ArrowRight, ArrowLeft } from 'lucide-react'

// Interest categories with icons and descriptions
const interestCategories = [
  {
    id: 'music',
    label: 'Music & Concerts',
    description: 'Live music, concerts, festivals',
    icon: Music,
    color: 'bg-purple-50 border-purple-200 text-purple-700'
  },
  {
    id: 'food',
    label: 'Food & Dining',
    description: 'Restaurants, food festivals, cooking',
    icon: Utensils,
    color: 'bg-orange-50 border-orange-200 text-orange-700'
  },
  {
    id: 'arts',
    label: 'Arts & Culture',
    description: 'Museums, galleries, theater',
    icon: Palette,
    color: 'bg-pink-50 border-pink-200 text-pink-700'
  },
  {
    id: 'sports',
    label: 'Sports & Fitness',
    description: 'Sports events, fitness activities',
    icon: Heart,
    color: 'bg-red-50 border-red-200 text-red-700'
  },
  {
    id: 'technology',
    label: 'Technology',
    description: 'Tech conferences, workshops',
    icon: Briefcase,
    color: 'bg-blue-50 border-blue-200 text-blue-700'
  },
  {
    id: 'education',
    label: 'Education & Learning',
    description: 'Workshops, seminars, courses',
    icon: GraduationCap,
    color: 'bg-green-50 border-green-200 text-green-700'
  },
  {
    id: 'photography',
    label: 'Photography',
    description: 'Photo walks, exhibitions',
    icon: Camera,
    color: 'bg-indigo-50 border-indigo-200 text-indigo-700'
  },
  {
    id: 'gaming',
    label: 'Gaming & Esports',
    description: 'Gaming tournaments, conventions',
    icon: Gamepad2,
    color: 'bg-cyan-50 border-cyan-200 text-cyan-700'
  },
  {
    id: 'travel',
    label: 'Travel & Adventure',
    description: 'Travel meetups, adventure activities',
    icon: Plane,
    color: 'bg-teal-50 border-teal-200 text-teal-700'
  },
  {
    id: 'networking',
    label: 'Networking & Social',
    description: 'Professional networking, social events',
    icon: Users,
    color: 'bg-yellow-50 border-yellow-200 text-yellow-700'
  }
]

export default function SelectInterestsPage() {
  const router = useRouter()
  const { data: session, status } = useSession()
  const searchParams = useSearchParams()
  const [selectedInterests, setSelectedInterests] = useState<string[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | undefined>('')
  const [success, setSuccess] = useState<string | undefined>('')

  const role = searchParams?.get('role') || 'USER'
  const isFirstTime = searchParams?.get('firstTime') === 'true'

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/login')
    }
  }, [status, router])

  const handleInterestToggle = (interestId: string) => {
    setSelectedInterests(prev => 
      prev.includes(interestId)
        ? prev.filter(id => id !== interestId)
        : [...prev, interestId]
    )
  }

  const handleSubmit = async () => {
    if (selectedInterests.length === 0) {
      setError('Please select at least one interest to continue')
      return
    }

    setIsLoading(true)
    setError('')
    setSuccess('')

    try {
      const response = await fetch('/api/auth/update-interests', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: session?.user?.id,
          interests: selectedInterests,
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        setError(data.error || 'Failed to save interests')
      } else {
        setSuccess('Interests saved successfully!')
        toast.success('Great! Your interests have been saved.')
        
        // Redirect to onboarding after a short delay
        setTimeout(() => {
          router.push(`/auth/onboarding?role=${role}&firstTime=${isFirstTime}`)
        }, 1000)
      }
    } catch (err) {
      setError('An unexpected error occurred')
    } finally {
      setIsLoading(false)
    }
  }

  const handleSkip = () => {
    router.push(`/auth/onboarding?role=${role}&firstTime=${isFirstTime}`)
  }

  const handleBack = () => {
    router.push(`/auth/select-role?firstTime=${isFirstTime}`)
  }

  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen py-8">
      <div className="container flex items-center justify-center">
        <Card className="w-full max-w-4xl shadow-xl">
          <CardHeader className="text-center space-y-4 pb-6">
            <CardTitle className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
              What interests you?
            </CardTitle>
            <CardDescription className="text-base text-gray-600">
              Select the types of events and activities you're most interested in. This helps us personalize your experience.
            </CardDescription>
            <div className="flex items-center justify-center space-x-2">
              <Badge variant="outline" className="text-xs">
                {selectedInterests.length} selected
              </Badge>
              {selectedInterests.length >= 3 && (
                <Badge className="text-xs bg-green-100 text-green-700">
                  Great selection!
                </Badge>
              )}
            </div>
          </CardHeader>

          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {interestCategories.map((interest) => {
                const Icon = interest.icon
                const isSelected = selectedInterests.includes(interest.id)
                
                return (
                  <button
                    key={interest.id}
                    type="button"
                    onClick={() => handleInterestToggle(interest.id)}
                    className={`p-4 border-2 rounded-xl text-left transition-all duration-200 hover:shadow-md ${
                      isSelected 
                        ? `${interest.color} border-current ring-2 ring-offset-2 ring-blue-500` 
                        : 'bg-white border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="flex items-start space-x-3">
                      <div className={`p-2 rounded-lg ${isSelected ? 'bg-white/50' : 'bg-gray-50'}`}>
                        <Icon className="h-5 w-5" />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center justify-between">
                          <h3 className="font-semibold text-sm">{interest.label}</h3>
                          <div className={`w-5 h-5 rounded border-2 flex items-center justify-center ${
                            isSelected
                              ? 'bg-blue-600 border-blue-600'
                              : 'border-gray-300'
                          }`}>
                            {isSelected && <Check className="w-3 h-3 text-white" />}
                          </div>
                        </div>
                        <p className="text-xs text-gray-600 mt-1">{interest.description}</p>
                      </div>
                    </div>
                  </button>
                )
              })}
            </div>

            {error && <FormError message={error} />}
            {success && <FormSuccess message={success} />}
          </CardContent>

          <CardFooter className="flex flex-col sm:flex-row gap-3 justify-between px-6 py-6 bg-gray-50 rounded-b-lg">
            <div className="flex gap-3">
              <Button
                variant="outline"
                onClick={handleBack}
                disabled={isLoading}
                className="w-full sm:w-auto"
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back
              </Button>

            </div>
            <Button
              onClick={handleSubmit}
              disabled={isLoading || selectedInterests.length === 0}
              className="w-full sm:w-auto bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700"
            >
              {isLoading ? 'Saving...' : 'Continue'}
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </CardFooter>
        </Card>
      </div>
    </div>
  )
}
