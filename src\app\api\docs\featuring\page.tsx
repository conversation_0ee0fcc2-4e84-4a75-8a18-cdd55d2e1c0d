import { Metada<PERSON> } from 'next';
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, Star, Code, FileText, Copy, Check } from 'lucide-react';
import { CodeBlock } from '@/components/ui/code-block';

export const metadata: Metadata = {
  title: 'API Documentation - Featuring',
  description: 'Documentation for the Event Featuring API',
};

export default function FeaturingApiDocsPage() {
  return (
    <div className="container mx-auto py-10">
      <div className="flex items-center mb-6">
        <Button variant="ghost" size="sm" asChild className="mr-4">
          <Link href="/api/docs">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to API Docs
          </Link>
        </Button>
        <Badge className="bg-yellow-500 text-white">
          <Star className="mr-1 h-3 w-3 fill-white" />
          Featured Events API
        </Badge>
      </div>
      
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold mb-2">Featured Events API</h1>
          <p className="text-gray-500 mb-4">
            Access featured events and related data through our API
          </p>
        </div>
        
        <Card>
          <CardHeader>
            <CardTitle>Overview</CardTitle>
            <CardDescription>
              The Featured Events API allows you to access events that are currently featured on our platform.
              Featured events receive premium placement and visibility.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <p>
              Featured events are events that organizers have paid to promote on our platform.
              They receive premium placement on the landing page and in search results.
              You can use this API to:
            </p>
            <ul className="list-disc pl-6 space-y-2">
              <li>Get a list of all published events, with an option to filter for featured events only</li>
              <li>Get details about an event's featuring status and tier</li>
              <li>Sort events with featured events appearing first</li>
            </ul>
          </CardContent>
        </Card>
        
        <Tabs defaultValue="endpoints">
          <TabsList>
            <TabsTrigger value="endpoints">Endpoints</TabsTrigger>
            <TabsTrigger value="examples">Examples</TabsTrigger>
            <TabsTrigger value="models">Data Models</TabsTrigger>
          </TabsList>
          
          <TabsContent value="endpoints" className="space-y-6 mt-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center">
                      <Code className="mr-2 h-5 w-5" />
                      GET /api/events/published
                    </CardTitle>
                    <CardDescription>
                      Get a list of published events with featuring information
                    </CardDescription>
                  </div>
                  <Badge>GET</Badge>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h3 className="text-sm font-medium mb-2">Query Parameters</h3>
                  <div className="rounded-md border">
                    <div className="grid grid-cols-4 gap-4 p-4 font-medium border-b">
                      <div>Parameter</div>
                      <div>Type</div>
                      <div>Default</div>
                      <div>Description</div>
                    </div>
                    
                    <div className="grid grid-cols-4 gap-4 p-4 border-b">
                      <div className="font-medium">featured</div>
                      <div>boolean</div>
                      <div>false</div>
                      <div>If true, returns only featured events</div>
                    </div>
                    
                    <div className="grid grid-cols-4 gap-4 p-4 border-b">
                      <div className="font-medium">category</div>
                      <div>string</div>
                      <div>null</div>
                      <div>Filter events by category</div>
                    </div>
                    
                    <div className="grid grid-cols-4 gap-4 p-4 border-b">
                      <div className="font-medium">type</div>
                      <div>string</div>
                      <div>null</div>
                      <div>Filter events by type (PHYSICAL, VIRTUAL, HYBRID)</div>
                    </div>
                    
                    <div className="grid grid-cols-4 gap-4 p-4 border-b">
                      <div className="font-medium">page</div>
                      <div>integer</div>
                      <div>1</div>
                      <div>Page number for pagination</div>
                    </div>
                    
                    <div className="grid grid-cols-4 gap-4 p-4">
                      <div className="font-medium">limit</div>
                      <div>integer</div>
                      <div>10</div>
                      <div>Number of events per page (max 50)</div>
                    </div>
                  </div>
                </div>
                
                <div>
                  <h3 className="text-sm font-medium mb-2">Response</h3>
                  <p className="text-sm text-gray-500 mb-4">
                    Returns a list of events with featuring information. Featured events are sorted first.
                  </p>
                  
                  <CodeBlock
                    language="json"
                    code={`{
  "events": [
    {
      "id": "clg2x3z9p0000abc123def456",
      "title": "Summer Music Festival",
      "description": "A weekend of amazing music and fun",
      "startDate": "2023-07-15T18:00:00.000Z",
      "endDate": "2023-07-17T22:00:00.000Z",
      "venue": "Central Park",
      "category": "CONCERT",
      "imagePath": "/uploads/events/summer-fest.jpg",
      "imageUrl": "https://example.com/api/images/uploads/events/summer-fest.jpg",
      "isFeatured": true,
      "featuringTier": "ELITE",
      "organizer": {
        "id": "clg2x3z9p0001abc123def457",
        "name": "Event Promotions Inc."
      }
    },
    {
      "id": "clg2x3z9p0002abc123def458",
      "title": "Tech Conference 2023",
      "description": "The latest in technology trends",
      "startDate": "2023-08-10T09:00:00.000Z",
      "endDate": "2023-08-12T17:00:00.000Z",
      "venue": "Convention Center",
      "category": "CONFERENCE",
      "imagePath": "/uploads/events/tech-conf.jpg",
      "imageUrl": "https://example.com/api/images/uploads/events/tech-conf.jpg",
      "isFeatured": true,
      "featuringTier": "PREMIUM",
      "organizer": {
        "id": "clg2x3z9p0003abc123def459",
        "name": "TechEvents LLC"
      }
    },
    {
      "id": "clg2x3z9p0004abc123def460",
      "title": "Community Workshop",
      "description": "Learn new skills with your community",
      "startDate": "2023-06-20T10:00:00.000Z",
      "endDate": "2023-06-20T16:00:00.000Z",
      "venue": "Community Center",
      "category": "WORKSHOP",
      "imagePath": "/uploads/events/workshop.jpg",
      "imageUrl": "https://example.com/api/images/uploads/events/workshop.jpg",
      "isFeatured": false,
      "featuringTier": null,
      "organizer": {
        "id": "clg2x3z9p0005abc123def461",
        "name": "Community Org"
      }
    }
  ],
  "pagination": {
    "total": 42,
    "pages": 5,
    "page": 1,
    "limit": 10
  }
}`}
                  />
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center">
                      <Code className="mr-2 h-5 w-5" />
                      GET /api/events/{'{id}'}
                    </CardTitle>
                    <CardDescription>
                      Get details for a specific event, including featuring information
                    </CardDescription>
                  </div>
                  <Badge>GET</Badge>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h3 className="text-sm font-medium mb-2">Path Parameters</h3>
                  <div className="rounded-md border">
                    <div className="grid grid-cols-3 gap-4 p-4 font-medium border-b">
                      <div>Parameter</div>
                      <div>Type</div>
                      <div>Description</div>
                    </div>
                    
                    <div className="grid grid-cols-3 gap-4 p-4">
                      <div className="font-medium">id</div>
                      <div>string</div>
                      <div>The unique identifier of the event</div>
                    </div>
                  </div>
                </div>
                
                <div>
                  <h3 className="text-sm font-medium mb-2">Response</h3>
                  <p className="text-sm text-gray-500 mb-4">
                    Returns detailed information about the event, including its featuring status.
                  </p>
                  
                  <CodeBlock
                    language="json"
                    code={`{
  "id": "clg2x3z9p0000abc123def456",
  "title": "Summer Music Festival",
  "description": "A weekend of amazing music and fun",
  "startDate": "2023-07-15T18:00:00.000Z",
  "endDate": "2023-07-17T22:00:00.000Z",
  "venue": "Central Park",
  "category": "CONCERT",
  "eventType": "PHYSICAL",
  "imagePath": "/uploads/events/summer-fest.jpg",
  "imageUrl": "https://example.com/api/images/uploads/events/summer-fest.jpg",
  "isFeatured": true,
  "featuring": {
    "tier": "ELITE",
    "startDate": "2023-06-01T00:00:00.000Z",
    "endDate": "2023-07-31T23:59:59.000Z",
    "status": "ACTIVE"
  },
  "organizer": {
    "id": "clg2x3z9p0001abc123def457",
    "name": "Event Promotions Inc.",
    "image": "https://example.com/api/images/uploads/users/organizer.jpg"
  },
  "tickets": [
    {
      "id": "clg2x3z9p0006abc123def462",
      "name": "General Admission",
      "price": 49.99,
      "quantity": 1000,
      "quantitySold": 450
    },
    {
      "id": "clg2x3z9p0007abc123def463",
      "name": "VIP Pass",
      "price": 149.99,
      "quantity": 100,
      "quantitySold": 75
    }
  ]
}`}
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="examples" className="space-y-6 mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Example: Get Featured Events</CardTitle>
                <CardDescription>
                  How to fetch only featured events using the API
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h3 className="text-sm font-medium mb-2">Request</h3>
                  <CodeBlock
                    language="bash"
                    code={`curl -X GET "https://example.com/api/events/published?featured=true" \\
  -H "x-api-key: your_api_key"`}
                  />
                </div>
                
                <div>
                  <h3 className="text-sm font-medium mb-2">JavaScript Example</h3>
                  <CodeBlock
                    language="javascript"
                    code={`// Using fetch API
const fetchFeaturedEvents = async () => {
  const response = await fetch(
    'https://example.com/api/events/published?featured=true',
    {
      headers: {
        'x-api-key': 'your_api_key'
      }
    }
  );
  
  const data = await response.json();
  return data.events;
};

// Using axios
import axios from 'axios';

const fetchFeaturedEvents = async () => {
  const response = await axios.get(
    'https://example.com/api/events/published?featured=true',
    {
      headers: {
        'x-api-key': 'your_api_key'
      }
    }
  );
  
  return response.data.events;
};`}
                  />
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>Example: Filter Featured Events by Category</CardTitle>
                <CardDescription>
                  How to fetch featured events in a specific category
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h3 className="text-sm font-medium mb-2">Request</h3>
                  <CodeBlock
                    language="bash"
                    code={`curl -X GET "https://example.com/api/events/published?featured=true&category=CONCERT" \\
  -H "x-api-key: your_api_key"`}
                  />
                </div>
                
                <div>
                  <h3 className="text-sm font-medium mb-2">Python Example</h3>
                  <CodeBlock
                    language="python"
                    code={`import requests

def fetch_featured_concerts():
    url = "https://example.com/api/events/published"
    params = {
        "featured": "true",
        "category": "CONCERT"
    }
    headers = {
        "x-api-key": "your_api_key"
    }
    
    response = requests.get(url, params=params, headers=headers)
    data = response.json()
    
    return data["events"]

# Example usage
featured_concerts = fetch_featured_concerts()
for event in featured_concerts:
    print(f"{event['title']} - {event['featuringTier']} tier")`}
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="models" className="space-y-6 mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Event Model</CardTitle>
                <CardDescription>
                  The structure of event data returned by the API
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="rounded-md border">
                  <div className="grid grid-cols-3 gap-4 p-4 font-medium border-b">
                    <div>Field</div>
                    <div>Type</div>
                    <div>Description</div>
                  </div>
                  
                  <div className="grid grid-cols-3 gap-4 p-4 border-b">
                    <div className="font-medium">id</div>
                    <div>string</div>
                    <div>Unique identifier for the event</div>
                  </div>
                  
                  <div className="grid grid-cols-3 gap-4 p-4 border-b">
                    <div className="font-medium">title</div>
                    <div>string</div>
                    <div>The title of the event</div>
                  </div>
                  
                  <div className="grid grid-cols-3 gap-4 p-4 border-b">
                    <div className="font-medium">description</div>
                    <div>string</div>
                    <div>Description of the event</div>
                  </div>
                  
                  <div className="grid grid-cols-3 gap-4 p-4 border-b">
                    <div className="font-medium">startDate</div>
                    <div>string (ISO 8601)</div>
                    <div>Start date and time of the event</div>
                  </div>
                  
                  <div className="grid grid-cols-3 gap-4 p-4 border-b">
                    <div className="font-medium">endDate</div>
                    <div>string (ISO 8601)</div>
                    <div>End date and time of the event</div>
                  </div>
                  
                  <div className="grid grid-cols-3 gap-4 p-4 border-b">
                    <div className="font-medium">venue</div>
                    <div>string</div>
                    <div>Location where the event will be held</div>
                  </div>
                  
                  <div className="grid grid-cols-3 gap-4 p-4 border-b">
                    <div className="font-medium">category</div>
                    <div>string</div>
                    <div>Category of the event (e.g., CONCERT, CONFERENCE)</div>
                  </div>
                  
                  <div className="grid grid-cols-3 gap-4 p-4 border-b">
                    <div className="font-medium">imagePath</div>
                    <div>string</div>
                    <div>Path to the event image</div>
                  </div>
                  
                  <div className="grid grid-cols-3 gap-4 p-4 border-b">
                    <div className="font-medium">imageUrl</div>
                    <div>string</div>
                    <div>Full URL to the event image</div>
                  </div>
                  
                  <div className="grid grid-cols-3 gap-4 p-4 border-b">
                    <div className="font-medium">isFeatured</div>
                    <div>boolean</div>
                    <div>Whether the event is currently featured</div>
                  </div>
                  
                  <div className="grid grid-cols-3 gap-4 p-4 border-b">
                    <div className="font-medium">featuringTier</div>
                    <div>string | null</div>
                    <div>The featuring tier (BASIC, PREMIUM, ELITE) or null if not featured</div>
                  </div>
                  
                  <div className="grid grid-cols-3 gap-4 p-4">
                    <div className="font-medium">organizer</div>
                    <div>object</div>
                    <div>Information about the event organizer</div>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>Featuring Tiers</CardTitle>
                <CardDescription>
                  The different tiers of event featuring
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="rounded-md border">
                  <div className="grid grid-cols-2 gap-4 p-4 font-medium border-b">
                    <div>Tier</div>
                    <div>Description</div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4 p-4 border-b">
                    <div className="font-medium">BASIC</div>
                    <div>Standard placement on the landing page</div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4 p-4 border-b">
                    <div className="font-medium">PREMIUM</div>
                    <div>Priority placement with enhanced visibility</div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4 p-4">
                    <div className="font-medium">ELITE</div>
                    <div>Top placement with maximum visibility and additional promotion</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
