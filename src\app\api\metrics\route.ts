// app/api/metrics/route.ts
import { NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';

export async function GET(request: Request) {
  try {
    const user = await currentUser();
    if (!user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get URL parameters
    const url = new URL(request.url);
    const timeFrame = url.searchParams.get('timeFrame') || 'month';

    // Get date range based on timeFrame
    const now = new Date();
    let startDate = new Date();

    switch(timeFrame) {
      case 'week':
        startDate.setDate(now.getDate() - 7);
        break;
      case 'month':
        startDate.setMonth(now.getMonth() - 1);
        break;
      case 'quarter':
        startDate.setMonth(now.getMonth() - 3);
        break;
      case 'year':
        startDate.setFullYear(now.getFullYear() - 1);
        break;
      default:
        startDate.setMonth(now.getMonth() - 1); // Default to month
    }

    // Get events for this organizer
    const events = await db.event.findMany({
      where: { userId: user.id },
      select: { id: true }
    });

    const eventIds = events.map(event => event.id);

    // Get ticket sales data
    const ticketSales = await db.ticketSales.findMany({
      where: {
        eventId: { in: eventIds },
        createdAt: { gte: startDate }
      },
      orderBy: { createdAt: 'asc' }
    });

    // Get analytics data
    const analyticsEntries = await db.analyticsEntry.findMany({
      where: {
        eventId: { in: eventIds },
        date: { gte: startDate }
      },
      orderBy: { date: 'asc' }
    });

    // Get engagement data
    const engagements = await db.engagement.findMany({
      where: {
        eventId: { in: eventIds },
        createdAt: { gte: startDate }
      },
      orderBy: { createdAt: 'asc' }
    });

    // Get audience data
    const audienceData = await db.audienceData.findMany({
      where: {
        eventId: { in: eventIds }
      }
    });

    // Calculate summary metrics
    const totalRevenue = ticketSales.reduce((acc, curr) => acc + curr.revenue, 0);
    const totalSold = ticketSales.reduce((acc, curr) => acc + curr.sold, 0);
    const averageTicketPrice = totalSold > 0 ? totalRevenue / totalSold : 0;
    const targetCompletion = ticketSales.reduce((acc, curr) => acc + curr.targetSales, 0) > 0 ?
      (totalSold / ticketSales.reduce((acc, curr) => acc + curr.targetSales, 0) * 100) : 0;

    // Get previous period data for growth calculations
    const previousPeriodStart = new Date(startDate);
    const periodDuration = now.getTime() - startDate.getTime();
    previousPeriodStart.setTime(startDate.getTime() - periodDuration);

    const previousPeriodTicketSales = await db.ticketSales.findMany({
      where: {
        eventId: { in: eventIds },
        createdAt: {
          gte: previousPeriodStart,
          lt: startDate
        }
      }
    });

    const previousPeriodRevenue = previousPeriodTicketSales.reduce((acc, curr) => acc + curr.revenue, 0);
    const previousPeriodSold = previousPeriodTicketSales.reduce((acc, curr) => acc + curr.sold, 0);
    const previousPeriodAvgPrice = previousPeriodSold > 0 ? previousPeriodRevenue / previousPeriodSold : 0;
    const previousPeriodTargetSales = previousPeriodTicketSales.reduce((acc, curr) => acc + curr.targetSales, 0);
    const previousPeriodCompletion = previousPeriodTargetSales > 0 ?
      (previousPeriodSold / previousPeriodTargetSales * 100) : 0;

    // Calculate growth metrics
    const growth = {
      revenue: previousPeriodRevenue > 0 ? ((totalRevenue - previousPeriodRevenue) / previousPeriodRevenue * 100) : 0,
      tickets: previousPeriodSold > 0 ? ((totalSold - previousPeriodSold) / previousPeriodSold * 100) : 0,
      price: previousPeriodAvgPrice > 0 ? ((averageTicketPrice - previousPeriodAvgPrice) / previousPeriodAvgPrice * 100) : 0,
      target: previousPeriodCompletion > 0 ? ((targetCompletion - previousPeriodCompletion) / previousPeriodCompletion * 100) : 0,
    };

    // Prepare time series data for charts
    const salesByDay: Record<string, number> = {};
    const revenueByDay: Record<string, number> = {};
    const engagementByDay: Record<string, { views: number; clicks: number; shares: number; likes: number }> = {};

    // Process ticket sales data by day
    ticketSales.forEach(sale => {
      const date = sale.createdAt.toISOString().split('T')[0];
      if (!salesByDay[date]) salesByDay[date] = 0;
      if (!revenueByDay[date]) revenueByDay[date] = 0;

      salesByDay[date] += sale.sold;
      revenueByDay[date] += sale.revenue;
    });

    // Process engagement data by day
    engagements.forEach(engagement => {
      const date = engagement.createdAt.toISOString().split('T')[0];
      if (!engagementByDay[date]) {
        engagementByDay[date] = {
          views: 0,
          clicks: 0,
          shares: 0,
          likes: 0
        };
      }

      engagementByDay[date].views += engagement.views;
      engagementByDay[date].clicks += engagement.clicks;
      engagementByDay[date].shares += engagement.shares;
      engagementByDay[date].likes += engagement.likes;
    });

    // Combine data for time series chart
    const timeSeriesData = Object.keys(salesByDay).map(date => ({
      date,
      sales: salesByDay[date] || 0,
      revenue: revenueByDay[date] || 0,
      views: engagementByDay[date]?.views || 0,
      clicks: engagementByDay[date]?.clicks || 0,
      shares: engagementByDay[date]?.shares || 0,
      likes: engagementByDay[date]?.likes || 0,
    })).sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

    // Prepare ticket type distribution data
    const ticketTypeDistribution = ticketSales.reduce<Record<string, { type: string; sold: number; revenue: number }>>((acc, curr) => {
      if (!acc[curr.type]) {
        acc[curr.type] = {
          type: curr.type,
          sold: 0,
          revenue: 0
        };
      }
      acc[curr.type].sold += curr.sold;
      acc[curr.type].revenue += curr.revenue;
      return acc;
    }, {});

    // Format audience data for charts
    const formattedAudienceData = audienceData.map(item => ({
      age: item.age,
      percentage: item.percentage,
      engagement: item.engagement
    }));

    return NextResponse.json({
      data: {
        // Summary metrics
        totalRevenue,
        totalSold,
        averageTicketPrice,
        targetCompletion,
        lastMonthGrowth: growth,

        // Chart data
        timeSeriesData,
        ticketTypeDistribution: Object.values(ticketTypeDistribution),
        audienceData: formattedAudienceData,

        // Engagement summary
        totalEngagement: engagements.reduce((acc, curr) => acc + curr.views + curr.clicks + curr.shares + curr.likes, 0),
        engagementBreakdown: {
          views: engagements.reduce((acc, curr) => acc + curr.views, 0),
          clicks: engagements.reduce((acc, curr) => acc + curr.clicks, 0),
          shares: engagements.reduce((acc, curr) => acc + curr.shares, 0),
          likes: engagements.reduce((acc, curr) => acc + curr.likes, 0),
        }
      },
      message: 'Metrics retrieved successfully',
      status: 200,
    });
  } catch (error) {
    console.error('Error calculating metrics:', error);
    return NextResponse.json(
      { message: 'Internal server error', status: 500 },
      { status: 500 }
    );
  }
}