# Vendor Featuring Payment System

## Overview

The Vendor Featuring Payment System allows vendors to pay to feature their businesses on the homepage, increasing visibility and attracting more customers. The system provides automatic featuring after payment without requiring admin approval.

## Features

- **Automatic Featuring**: Vendors are automatically featured after successful payment
- **Multiple Tiers**: Basic ($19.99/week), Premium ($49.99/week), Elite ($99.99/week)
- **Duration Discounts**: Up to 30% discount for longer durations
- **Expiration Management**: Automatic unfeaturing when featuring expires
- **Payment Integration**: Stripe payment processing with webhooks
- **Simulation Mode**: Testing mode for development

## Architecture

### Database Schema

```sql
model VendorFeaturing {
  id               String            @id @default(cuid())
  vendorId         String
  tier             FeaturingTier     // BASIC, PREMIUM, ELITE
  startDate        DateTime
  endDate          DateTime
  status           FeaturingStatus   @default(PENDING) // PENDING, ACTIVE, EXPIRED, CANCELLED
  paymentAmount    Decimal
  paymentId        String?
  metadata         Json?
  createdAt        DateTime          @default(now())
  updatedAt        DateTime          @updatedAt
  vendor           VendorProfile     @relation(fields: [vendorId], references: [id], onDelete: Cascade)
}
```

### API Endpoints

#### Create Featuring Payment
```
POST /api/vendors/[id]/feature
```
Creates a payment intent for vendor featuring.

**Request Body:**
```json
{
  "tier": "PREMIUM",
  "durationDays": 30
}
```

**Response:**
```json
{
  "success": true,
  "paymentIntent": {
    "id": "pi_...",
    "clientSecret": "pi_..._secret_..."
  },
  "featuring": {
    "id": "feat_...",
    "tier": "PREMIUM",
    "startDate": "2024-01-01T00:00:00Z",
    "endDate": "2024-01-31T00:00:00Z",
    "amount": 199.96,
    "durationDays": 30
  }
}
```

#### Get Featuring Status
```
GET /api/vendors/[id]/feature
```
Returns vendor featuring status and history.

#### Simulation Endpoints (Testing)
```
POST /api/vendors/[id]/feature/simulation
POST /api/vendors/[id]/feature/simulation/complete
```

### Featuring Tiers

| Tier | Price/Week | Features |
|------|------------|----------|
| **Basic** | $19.99 | Featured on homepage, Basic placement, Standard design |
| **Premium** | $49.99 | Priority placement, Highlighted design, Newsletter inclusion |
| **Elite** | $99.99 | Top placement, Premium design, Social media promotion |

### Duration Discounts

| Duration | Discount |
|----------|----------|
| 1 week | 0% |
| 2 weeks | 10% |
| 1 month | 20% |
| 2 months | 25% |
| 3 months | 30% |

## Payment Flow

1. **Vendor selects tier and duration**
2. **System calculates total price** (including discounts)
3. **Payment intent created** via Stripe
4. **Vendor completes payment**
5. **Stripe webhook triggers** automatic featuring
6. **Vendor profile updated** with `featured: true`
7. **Notification sent** to vendor

## Automatic Expiration

- **Cron job** runs daily to check for expired featurings
- **Expired featurings** are marked as `EXPIRED`
- **Vendor profiles** are updated to `featured: false`
- **Notifications** are sent to vendors

### Cron Endpoint
```
GET /api/cron/vendor-featuring-expiration
Authorization: Bearer <CRON_SECRET>
```

## Frontend Components

### FeatureVendorButton
```tsx
<FeatureVendorButton
  vendorId="vendor_123"
  businessName="My Business"
  size="lg"
/>
```

### Vendor Dashboard
- **Current Status**: Shows active featuring details
- **Benefits Overview**: Explains featuring tiers
- **History**: Past featuring campaigns

## Integration with Vendor Listing

Vendors are displayed with:
- **Priority ordering**: Featured vendors appear first
- **Tier badges**: Visual indicators (Basic/Premium/Elite)
- **Expiration handling**: Automatic unfeaturing

## Testing

### Simulation Mode
For development and testing, use simulation endpoints:

```javascript
// Create simulation
const response = await fetch(`/api/vendors/${vendorId}/feature/simulation`, {
  method: 'POST',
  body: JSON.stringify({ tier: 'PREMIUM', durationDays: 7 })
});

// Complete simulation
await fetch(`/api/vendors/${vendorId}/feature/simulation/complete`, {
  method: 'POST',
  body: JSON.stringify({ 
    tier: 'PREMIUM', 
    durationDays: 7,
    paymentIntentId: 'sim_pi_vendor_...'
  })
});
```

## Setup Instructions

1. **Update Database Schema**
   ```bash
   npx prisma db push
   ```

2. **Configure Stripe Webhooks**
   - Add webhook endpoint: `/api/webhooks/stripe`
   - Enable `payment_intent.succeeded` event

3. **Set Environment Variables**
   ```env
   STRIPE_SECRET_KEY=sk_...
   STRIPE_WEBHOOK_SECRET=whsec_...
   CRON_SECRET=your_cron_secret
   ```

4. **Setup Cron Job**
   Configure daily cron job to call:
   ```
   GET /api/cron/vendor-featuring-expiration
   ```

## Security

- **Authentication**: Only vendor owners can feature their businesses
- **Verification**: Vendors must be verified before featuring
- **Payment Security**: Stripe handles all payment processing
- **Webhook Verification**: Stripe webhook signatures are verified

## Monitoring

- **Payment Success/Failure**: Logged via Stripe webhooks
- **Expiration Processing**: Logged via cron job
- **Notifications**: Sent for featuring approval and expiration

## Future Enhancements

- **Analytics Dashboard**: Featuring performance metrics
- **A/B Testing**: Different featuring layouts
- **Bulk Discounts**: Volume pricing for multiple vendors
- **Recurring Payments**: Subscription-based featuring
