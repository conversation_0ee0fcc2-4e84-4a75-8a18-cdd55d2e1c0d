import { NextRequest } from 'next/server';
import { db } from '@/lib/prisma';
import { withMonetization, createApiResponse } from '@/lib/api-monetization';
import { corsPreflightResponse } from '@/lib/cors';

export const dynamic = 'force-dynamic';

/**
 * GET /api/monetized/analytics/events
 * Get analytics data for events
 */
export const GET = withMonetization(async (request: NextRequest, context: any, apiKeyData: any) => {
  try {
    const { searchParams } = new URL(request.url);
    const period = searchParams.get('period') || 'month'; // day, week, month, year
    const category = searchParams.get('category');
    
    // Calculate date ranges based on period
    const now = new Date();
    let startDate = new Date();
    
    switch (period) {
      case 'day':
        startDate.setDate(now.getDate() - 1);
        break;
      case 'week':
        startDate.setDate(now.getDate() - 7);
        break;
      case 'month':
        startDate.setMonth(now.getMonth() - 1);
        break;
      case 'year':
        startDate.setFullYear(now.getFullYear() - 1);
        break;
      default:
        startDate.setMonth(now.getMonth() - 1); // Default to month
    }
    
    // Build the where clause for events
    const eventWhere: any = {
      status: 'Published',
      startDate: { gte: startDate },
    };
    
    if (category) {
      eventWhere.category = category;
    }
    
    // Get events created in the period
    const eventsCreated = await db.event.count({
      where: {
        ...eventWhere,
        createdAt: { gte: startDate },
      },
    });
    
    // Get total events
    const totalEvents = await db.event.count({
      where: eventWhere,
    });
    
    // Get events by category
    const eventsByCategory = await db.event.groupBy({
      by: ['category'],
      where: eventWhere,
      _count: true,
      orderBy: {
        _count: {
          id: 'desc',
        },
      },
    });
    
    // Get events by type
    const eventsByType = await db.event.groupBy({
      by: ['eventType'],
      where: eventWhere,
      _count: true,
      orderBy: {
        _count: {
          id: 'desc',
        },
      },
    });
    
    // Get events by location (top 10)
    const eventsByLocation = await db.event.groupBy({
      by: ['location'],
      where: eventWhere,
      _count: true,
      orderBy: {
        _count: {
          id: 'desc',
        },
      },
      take: 10,
    });
    
    // Get upcoming events count
    const upcomingEvents = await db.event.count({
      where: {
        ...eventWhere,
        startDate: { gte: now },
      },
    });
    
    // Get past events count
    const pastEvents = await db.event.count({
      where: {
        ...eventWhere,
        endDate: { lt: now },
      },
    });
    
    // Get ongoing events count
    const ongoingEvents = await db.event.count({
      where: {
        ...eventWhere,
        startDate: { lte: now },
        endDate: { gte: now },
      },
    });
    
    // Format the response
    const formattedEventsByCategory = eventsByCategory.map(item => ({
      category: item.category,
      count: item._count,
    }));
    
    const formattedEventsByType = eventsByType.map(item => ({
      type: item.eventType,
      count: item._count,
    }));
    
    const formattedEventsByLocation = eventsByLocation.map(item => ({
      location: item.location,
      count: item._count,
    }));
    
    // Return the analytics data
    return createApiResponse(
      {
        period,
        timeRange: {
          start: startDate.toISOString(),
          end: now.toISOString(),
        },
        summary: {
          totalEvents,
          eventsCreated,
          upcomingEvents,
          ongoingEvents,
          pastEvents,
        },
        distribution: {
          byCategory: formattedEventsByCategory,
          byType: formattedEventsByType,
          byLocation: formattedEventsByLocation,
        },
      },
      200,
      apiKeyData.apiKey.id,
      request.nextUrl.pathname,
      request.method
    );
  } catch (error) {
    console.error('Error fetching event analytics:', error);
    return createApiResponse(
      { error: 'Failed to fetch event analytics' },
      500,
      apiKeyData.apiKey.id,
      request.nextUrl.pathname,
      request.method
    );
  }
}, ['read:analytics']);

/**
 * OPTIONS /api/monetized/analytics/events
 * Handle CORS preflight requests
 */
export async function OPTIONS(request: NextRequest) {
  return corsPreflightResponse('GET, OPTIONS', request);
}
