/*
  Warnings:

  - Made the column `minOrderQuantity` on table `PartnerProduct` required. This step will fail if there are existing NULL values in that column.
  - Made the column `sortOrder` on table `PartnerProduct` required. This step will fail if there are existing NULL values in that column.
  - Made the column `sortOrder` on table `PartnerProductCategory` required. This step will fail if there are existing NULL values in that column.

*/
-- AlterEnum
-- This migration adds more than one value to an enum.
-- With PostgreSQL versions 11 and earlier, this is not possible
-- in a single migration. This can be worked around by creating
-- multiple migrations, each migration adding only one value to
-- the enum.


ALTER TYPE "PartnerType" ADD VALUE 'CAFE';
ALTER TYPE "PartnerType" ADD VALUE 'LOUNGE';
ALTER TYPE "PartnerType" ADD VALUE 'VENUE';
ALTER TYPE "PartnerType" ADD VALUE 'CATERING';
ALTER TYPE "PartnerType" ADD VALUE 'TRANSPORT';
ALTER TYPE "PartnerType" ADD VALUE 'ENTERTAINMENT';
ALTER TYPE "PartnerType" ADD VALUE 'RETAIL';
ALTER TYPE "PartnerType" ADD VALUE 'SERVICE';
ALTER TYPE "PartnerType" ADD VALUE 'OTHER';

-- AlterTable
ALTER TABLE "PartnerProduct" ALTER COLUMN "minOrderQuantity" SET NOT NULL,
ALTER COLUMN "sortOrder" SET NOT NULL;

-- AlterTable
ALTER TABLE "PartnerProductCategory" ALTER COLUMN "sortOrder" SET NOT NULL;

-- CreateTable
CREATE TABLE "UserPreference" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "preferredEventCategories" "EventCategory"[],
    "preferredEventTypes" "EventType"[],
    "preferredPartnerTypes" "PartnerType"[],
    "preferredProductCategories" "ProductCategory"[],
    "tags" TEXT[],
    "bio" TEXT,
    "location" TEXT,
    "completed" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "UserPreference_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "VendorFeaturing" (
    "id" TEXT NOT NULL,
    "vendorId" TEXT NOT NULL,
    "tier" "FeaturingTier" NOT NULL,
    "startDate" TIMESTAMP(3) NOT NULL,
    "endDate" TIMESTAMP(3) NOT NULL,
    "status" "FeaturingStatus" NOT NULL DEFAULT 'PENDING',
    "paymentAmount" DECIMAL(65,30) NOT NULL,
    "paymentId" TEXT,
    "metadata" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "VendorFeaturing_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "UserPreference_userId_key" ON "UserPreference"("userId");

-- CreateIndex
CREATE INDEX "UserPreference_userId_idx" ON "UserPreference"("userId");

-- CreateIndex
CREATE INDEX "VendorFeaturing_vendorId_idx" ON "VendorFeaturing"("vendorId");

-- CreateIndex
CREATE INDEX "VendorFeaturing_tier_idx" ON "VendorFeaturing"("tier");

-- CreateIndex
CREATE INDEX "VendorFeaturing_status_idx" ON "VendorFeaturing"("status");

-- CreateIndex
CREATE INDEX "VendorFeaturing_startDate_idx" ON "VendorFeaturing"("startDate");

-- CreateIndex
CREATE INDEX "VendorFeaturing_endDate_idx" ON "VendorFeaturing"("endDate");

-- AddForeignKey
ALTER TABLE "UserPreference" ADD CONSTRAINT "UserPreference_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "VendorFeaturing" ADD CONSTRAINT "VendorFeaturing_vendorId_fkey" FOREIGN KEY ("vendorId") REFERENCES "VendorProfile"("id") ON DELETE CASCADE ON UPDATE CASCADE;
