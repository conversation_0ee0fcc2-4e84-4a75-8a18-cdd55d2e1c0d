import { NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';

export async function GET() {
  try {
    const user = await currentUser();

    if (!user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const now = new Date();

    // Get upcoming events for this organizer
    const upcomingEvents = await db.event.findMany({
      where: {
        userId: user.id!,
        startDate: { gte: now }
      },
      orderBy: { startDate: 'asc' },
      take: 5,
      select: {
        id: true,
        title: true,
        startDate: true,
        tickets: {
          select: {
            type: true,
            quantity: true,
            isUsed: true,
            regularSeats: true,
            vipSeats: true,
            vvipSeats: true
          }
        }
      },
    });

    // Format the events for the dashboard
    const formattedEvents = await Promise.all(upcomingEvents.map(async event => {
      // Calculate tickets sold
      const ticketsSold = event.tickets.length;

      // Calculate total capacity from regular and VIP seats from all tickets
      const regularSeats = event.tickets.reduce((sum, ticket) => sum + (ticket.regularSeats || 0), 0);
      const vipSeats = event.tickets.reduce((sum, ticket) => sum + (ticket.vipSeats || 0), 0);
      const capacity = regularSeats + vipSeats || 100; // Default to 100 if no seats are specified

      return {
        id: event.id,
        title: event.title,
        date: event.startDate.toISOString().split('T')[0],
        ticketsSold,
        capacity,
        status: 'upcoming',
      };
    }));

    return NextResponse.json(formattedEvents);
  } catch (error) {
    console.error('Error fetching upcoming events:', error);
    return NextResponse.json(
      { error: 'Failed to fetch upcoming events' },
      { status: 500 }
    );
  }
}


