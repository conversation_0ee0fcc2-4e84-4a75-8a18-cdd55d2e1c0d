import { NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';

export async function GET(request: Request) {
  try {
    const user = await currentUser();

    if (!user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get URL parameters
    const url = new URL(request.url);
    const isUsed = url.searchParams.get('isUsed');

    // Build the where clause
    const where: any = {
      event: {
        userId: user.id
      }
    };

    // Add isUsed filter if provided
    if (isUsed === 'true') {
      where.isUsed = true;
    } else if (isUsed === 'false') {
      where.isUsed = false;
    }

    // Fetch tickets for this organizer's events
    const tickets = await db.ticket.findMany({
      where,
      include: {
        event: {
          select: {
            id: true,
            title: true,
          },
        },
        Order: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    // Format the tickets for the frontend
    const formattedTickets = tickets.map(ticket => ({
      id: ticket.id,
      event: {
        id: ticket.event.id,
        title: ticket.event.title,
      },
      type: ticket.type,
      email: ticket.email,
      quantity: ticket.quantity || 1,
      isUsed: ticket.isUsed || false,
      totalPrice: ticket.totalPrice || 0,
      createdAt: ticket.createdAt.toISOString(),
      orderId: ticket.orderId || ticket.Order?.[0]?.id,
    }));

    return NextResponse.json(formattedTickets);
  } catch (error) {
    console.error('Error fetching tickets:', error);
    return NextResponse.json(
      { error: 'Failed to fetch tickets' },
      { status: 500 }
    );
  }
}
