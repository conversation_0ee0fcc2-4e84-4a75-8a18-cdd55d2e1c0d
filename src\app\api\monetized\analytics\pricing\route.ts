import { NextRequest } from 'next/server';
import { db } from '@/lib/prisma';
import { withMonetization, createApiResponse } from '@/lib/api-monetization';
import { corsPreflightResponse } from '@/lib/cors';

export const dynamic = 'force-dynamic';

/**
 * GET /api/monetized/analytics/pricing
 * Get ticket pricing insights
 */
export const GET = withMonetization(async (request: NextRequest, context: any, apiKeyData: any) => {
  try {
    const { searchParams } = new URL(request.url);
    const eventId = searchParams.get('eventId');
    const category = searchParams.get('category');
    const period = searchParams.get('period') || 'month'; // day, week, month, year
    
    // Calculate date range based on period
    const now = new Date();
    let startDate = new Date();
    
    switch (period) {
      case 'day':
        startDate.setDate(now.getDate() - 1);
        break;
      case 'week':
        startDate.setDate(now.getDate() - 7);
        break;
      case 'month':
        startDate.setMonth(now.getMonth() - 1);
        break;
      case 'year':
        startDate.setFullYear(now.getFullYear() - 1);
        break;
      default:
        startDate.setMonth(now.getMonth() - 1); // Default to month
    }
    
    // Build the where clause
    const where: any = {
      createdAt: {
        gte: startDate,
        lte: now,
      },
    };
    
    if (eventId) {
      where.eventId = eventId;
      
      // Check if the event exists
      const event = await db.event.findUnique({
        where: { id: eventId },
        select: { userId: true },
      });
      
      if (!event) {
        return createApiResponse(
          { error: 'Event not found' },
          404,
          apiKeyData.apiKey.id,
          request.nextUrl.pathname,
          request.method
        );
      }
      
      // Check if the user has permission to view pricing data for this event
      const isEventOrganizer = event.userId === apiKeyData.user.id;
      const isAdmin = apiKeyData.apiKey.permissions.includes('admin:analytics');
      
      if (!isEventOrganizer && !isAdmin) {
        return createApiResponse(
          { error: 'You do not have permission to view pricing data for this event' },
          403,
          apiKeyData.apiKey.id,
          request.nextUrl.pathname,
          request.method
        );
      }
    } else if (category) {
      where.event = {
        category,
      };
    }
    
    // Get ticket data
    const tickets = await db.ticket.findMany({
      where,
      include: {
        event: {
          select: {
            id: true,
            title: true,
            category: true,
          },
        },
      },
    });
    
    if (tickets.length === 0) {
      // If no ticket data is found, generate some sample data
      // In a real application, you would have actual ticket data
      return createApiResponse(
        {
          pricing: {
            averagePrice: 75.50,
            priceRange: {
              min: 25.00,
              max: 250.00,
              median: 65.00,
            },
            priceDistribution: [
              { range: '0-25', percentage: 10.5 },
              { range: '26-50', percentage: 25.8 },
              { range: '51-100', percentage: 42.3 },
              { range: '101-200', percentage: 18.2 },
              { range: '201+', percentage: 3.2 },
            ],
            priceTrends: [
              { month: 'Jan', averagePrice: 68.50 },
              { month: 'Feb', averagePrice: 70.25 },
              { month: 'Mar', averagePrice: 72.80 },
              { month: 'Apr', averagePrice: 75.50 },
              { month: 'May', averagePrice: 78.20 },
              { month: 'Jun', averagePrice: 80.75 },
            ],
            categoryComparison: [
              { category: 'MUSIC', averagePrice: 85.50 },
              { category: 'SPORTS', averagePrice: 95.75 },
              { category: 'ARTS', averagePrice: 65.25 },
              { category: 'BUSINESS', averagePrice: 120.50 },
              { category: 'FOOD', averagePrice: 55.25 },
            ],
            bestSellingPricePoints: [
              { price: 50.00, salesPercentage: 28.5 },
              { price: 75.00, salesPercentage: 22.3 },
              { price: 100.00, salesPercentage: 18.7 },
              { price: 25.00, salesPercentage: 15.2 },
              { price: 150.00, salesPercentage: 8.5 },
            ],
          },
          filters: {
            eventId,
            category,
            period,
          },
          note: 'This is sample data. In a production environment, this would be replaced with actual ticket pricing data.',
        },
        200,
        apiKeyData.apiKey.id,
        request.nextUrl.pathname,
        request.method
      );
    }
    
    // Calculate pricing metrics
    const prices = tickets.map(ticket => ticket.price);
    
    // Average price
    const averagePrice = prices.reduce((sum, price) => sum + price, 0) / prices.length;
    
    // Price range
    const minPrice = Math.min(...prices);
    const maxPrice = Math.max(...prices);
    
    // Median price
    const sortedPrices = [...prices].sort((a, b) => a - b);
    const medianPrice = sortedPrices.length % 2 === 0
      ? (sortedPrices[sortedPrices.length / 2 - 1] + sortedPrices[sortedPrices.length / 2]) / 2
      : sortedPrices[Math.floor(sortedPrices.length / 2)];
    
    // Price distribution
    const priceRanges = [
      { range: '0-25', min: 0, max: 25, count: 0 },
      { range: '26-50', min: 26, max: 50, count: 0 },
      { range: '51-100', min: 51, max: 100, count: 0 },
      { range: '101-200', min: 101, max: 200, count: 0 },
      { range: '201+', min: 201, max: Infinity, count: 0 },
    ];
    
    prices.forEach(price => {
      const range = priceRanges.find(r => price >= r.min && price <= r.max);
      if (range) {
        range.count++;
      }
    });
    
    const priceDistribution = priceRanges.map(range => ({
      range: range.range,
      percentage: parseFloat(((range.count / prices.length) * 100).toFixed(2)),
    }));
    
    // Group by category
    const categoryPrices: Record<string, { sum: number; count: number }> = {};
    
    tickets.forEach(ticket => {
      const category = ticket.event.category;
      
      if (!categoryPrices[category]) {
        categoryPrices[category] = { sum: 0, count: 0 };
      }
      
      categoryPrices[category].sum += ticket.price;
      categoryPrices[category].count++;
    });
    
    // Calculate average price by category
    const categoryComparison = Object.entries(categoryPrices).map(([category, data]) => ({
      category,
      averagePrice: parseFloat((data.sum / data.count).toFixed(2)),
    }));
    
    // Sort by average price (highest first)
    categoryComparison.sort((a, b) => b.averagePrice - a.averagePrice);
    
    // Best selling price points (simplified for this example)
    // In a real application, you would use actual sales data
    const pricePoints: Record<number, number> = {};
    
    tickets.forEach(ticket => {
      const price = Math.round(ticket.price); // Round to nearest dollar for grouping
      
      if (!pricePoints[price]) {
        pricePoints[price] = 0;
      }
      
      pricePoints[price]++;
    });
    
    const bestSellingPricePoints = Object.entries(pricePoints)
      .map(([price, count]) => ({
        price: parseInt(price),
        salesPercentage: parseFloat(((count / tickets.length) * 100).toFixed(2)),
      }))
      .sort((a, b) => b.salesPercentage - a.salesPercentage)
      .slice(0, 5); // Top 5 price points
    
    return createApiResponse(
      {
        pricing: {
          averagePrice: parseFloat(averagePrice.toFixed(2)),
          priceRange: {
            min: minPrice,
            max: maxPrice,
            median: parseFloat(medianPrice.toFixed(2)),
          },
          priceDistribution,
          categoryComparison,
          bestSellingPricePoints,
        },
        filters: {
          eventId,
          category,
          period,
        },
      },
      200,
      apiKeyData.apiKey.id,
      request.nextUrl.pathname,
      request.method
    );
  } catch (error) {
    console.error('Error fetching pricing data:', error);
    
    return createApiResponse(
      { error: 'Internal server error' },
      500,
      apiKeyData.apiKey.id,
      request.nextUrl.pathname,
      request.method
    );
  }
}, ['read:analytics']);

/**
 * OPTIONS /api/monetized/analytics/pricing
 * Handle CORS preflight requests
 */
export async function OPTIONS() {
  return corsPreflightResponse('GET, OPTIONS');
}
